<?php
defined('BASEPATH') or exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/userguide3/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'Dashboard/default';
$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;

$route['guest/listofattendance'] = 'Guest/ListOfAttendance';
$route['guest/listofattendance/scan'] = 'Guest/ListOfAttendance/scan';
$route['guest/listofattendance/scan/process'] = 'Guest/ListOfAttendance/process';

$route['sitemap'] = 'Sitemap';

$route['auth/login'] = 'Auth/login';
$route['auth/login/process'] = 'Auth/process_login';
$route['auth/logout'] = 'Auth/logout';
$route['auth/forgot'] = 'Auth/forgot';
$route['auth/forgot/process'] = 'Auth/process_forgot';
$route['auth/reset/(:any)'] = 'Auth/reset/$1';
$route['auth/reset/(:any)/process'] = 'Auth/process_reset/$1';

$route['account/profile'] = 'Account/profile';
$route['account/profile/process'] = 'Account/process_change_profile';

$route['account/password'] = 'Account/password';
$route['account/password/process'] = 'Account/process_change_password';

$route['dashboard/report'] = 'Dashboard/report';
$route['dashboard/userid'] = 'Dashboard/userid';
$route['dashboard/attendance'] = 'Dashboard/attendance';
$route['dashboard/export'] = 'Dashboard/export';

$route['master/select/kecamatan'] = 'Master/Select/kecamatan';
$route['master/select/desa'] = 'Master/Select/desa';

$route['master/superadmin'] = 'Master/SuperAdmin';
$route['master/superadmin/add'] = 'Master/SuperAdmin/add';
$route['master/superadmin/add/process'] = 'Master/SuperAdmin/process_add';
$route['master/superadmin/edit/(:num)'] = 'Master/SuperAdmin/edit/$1';
$route['master/superadmin/edit/(:num)/process'] = 'Master/SuperAdmin/process_edit/$1';

$route['master/pmd'] = 'Master/PMD';
$route['master/pmd/add'] = 'Master/PMD/add';
$route['master/pmd/add/process'] = 'Master/PMD/process_add';
$route['master/pmd/edit/(:num)'] = 'Master/PMD/edit/$1';
$route['master/pmd/edit/(:num)/process'] = 'Master/PMD/process_edit/$1';

$route['master/village'] = 'Master/Village';
$route['master/village/add'] = 'Master/Village/add';
$route['master/village/add/process'] = 'Master/Village/process_add';
$route['master/village/edit/(:num)'] = 'Master/Village/edit/$1';
$route['master/village/edit/(:num)/process'] = 'Master/Village/process_edit/$1';
$route['master/village/operator/(:num)'] = 'Master/Village/operator/$1';

$route['master/bpd'] = 'Master/BPD';
$route['master/bpd/add'] = 'Master/BPD/add';
$route['master/bpd/add/process'] = 'Master/BPD/process_add';
$route['master/bpd/edit/(:num)'] = 'Master/BPD/edit/$1';
$route['master/bpd/edit/(:num)/process'] = 'Master/BPD/process_edit/$1';
$route['master/bpd/monitoring'] = 'Master/BPD/monitoring';

$route['master/notulenrapat'] = 'Master/NotulenRapat';
$route['master/notulenrapat/add'] = 'Master/NotulenRapat/add';
$route['master/notulenrapat/add/process'] = 'Master/NotulenRapat/process_add';
$route['master/notulenrapat/edit/(:num)'] = 'Master/NotulenRapat/edit/$1';
$route['master/notulenrapat/edit/(:num)/process'] = 'Master/NotulenRapat/process_edit/$1';
$route['master/notulenrapat/export'] = 'Master/NotulenRapat/export';

$route['master/kegiatanbpd'] = 'Master/KegiatanBPD';
$route['master/kegiatanbpd/add'] = 'Master/KegiatanBPD/add';
$route['master/kegiatanbpd/add/process'] = 'Master/KegiatanBPD/process_add';
$route['master/kegiatanbpd/edit/(:num)'] = 'Master/KegiatanBPD/edit/$1';
$route['master/kegiatanbpd/edit/(:num)/process'] = 'Master/KegiatanBPD/process_edit/$1';
$route['master/kegiatanbpd/export'] = 'Master/KegiatanBPD/export';

$route['master/suratkeluar'] = 'Master/SuratKeluar';
$route['master/suratkeluar/export'] = 'Master/SuratKeluar/export';
$route['master/suratkeluar/add'] = 'Master/SuratKeluar/add';
$route['master/suratkeluar/add/process'] = 'Master/SuratKeluar/process_add';
$route['master/suratkeluar/edit/(:num)'] = 'Master/SuratKeluar/edit/$1';
$route['master/suratkeluar/edit/(:num)/process'] = 'Master/SuratKeluar/process_edit/$1';

$route['master/suratmasuk'] = 'Master/SuratMasuk';
$route['master/suratmasuk/export'] = 'Master/SuratMasuk/export';
$route['master/suratmasuk/add'] = 'Master/SuratMasuk/add';
$route['master/suratmasuk/add/process'] = 'Master/SuratMasuk/process_add';
$route['master/suratmasuk/edit/(:num)'] = 'Master/SuratMasuk/edit/$1';
$route['master/suratmasuk/edit/(:num)/process'] = 'Master/SuratMasuk/process_edit/$1';

$route['master/guestbook'] = 'Master/GuestBook';
$route['master/guestbook/export'] = 'Master/GuestBook/export';

$route['master/anggotabpd'] = 'Master/AnggotaBPD';
$route['master/anggotabpd/export'] = 'Master/AnggotaBPD/export';
$route['master/anggotabpd/sync'] = 'Master/AnggotaBPD/sync';

$route['master/aspirasimasyarakat'] = 'Master/AspirasiMasyarakat';
$route['master/aspirasimasyarakat/export'] = 'Master/AspirasiMasyarakat/export';

$route['master/daftarhadir'] = 'Master/DaftarHadir';
$route['master/daftarhadir/export'] = 'Master/DaftarHadir/export';
$route['master/daftarhadir/history'] = 'Master/DaftarHadir/history';

$route['master/keputusanbpd'] = 'Master/KeputusanBPD';
$route['master/keputusanbpd/export'] = 'Master/KeputusanBPD/export';

$route['master/peraturandesa'] = 'Master/PeraturanDesa';
$route['master/peraturandesa/export'] = 'Master/PeraturanDesa/export';

$route['master/operator'] = 'Master/Operator';
$route['master/operator/add'] = 'Master/Operator/add';
$route['master/operator/add/process'] = 'Master/Operator/process_add';
$route['master/operator/edit/(:num)'] = 'Master/Operator/edit/$1';
$route['master/operator/edit/(:num)/process'] = 'Master/Operator/process_edit/$1';
$route['master/operator/monitoring'] = 'Master/Operator/monitoring';

$route['master/kecamatan'] = 'Master/Kecamatan';
$route['master/kecamatan/add'] = 'Master/Kecamatan/add';
$route['master/kecamatan/add/process'] = 'Master/Kecamatan/process_add';
$route['master/kecamatan/edit/(:num)'] = 'Master/Kecamatan/edit/$1';
$route['master/kecamatan/edit/(:num)/process'] = 'Master/Kecamatan/process_edit/$1';

$route['master/kepdes'] = 'Master/KepDes';
$route['master/kepdes/process'] = 'Master/KepDes/process_kepdes';

$route['master/user_guide'] = 'Master/UserGuide';
$route['master/user_guide/add'] = 'Master/UserGuide/add';
$route['master/user_guide/add/process'] = 'Master/UserGuide/process_add';
$route['master/user_guide/edit/(:num)'] = 'Master/UserGuide/edit/$1';
$route['master/user_guide/edit/(:num)/process'] = 'Master/UserGuide/process_edit/$1';
$route['master/user_guide/delete'] = 'Master/UserGuide/process_delete';

$route['master/(:any)'] = 'CRUDCore/index/$1';
$route['master/(:any)/add'] = 'CRUDCore/add/$1';
$route['master/(:any)/add/process'] = 'CRUDCore/process_add/$1';
$route['master/(:any)/edit/(:num)'] = 'CRUDCore/edit/$1/$2';
$route['master/(:any)/edit/(:num)/process'] = 'CRUDCore/process_edit/$1/$2';
$route['master/(:any)/detail/(:num)'] = 'CRUDCore/detail/$1/$2';
$route['master/(:any)/delete'] = 'CRUDCore/process_delete/$1';

$route['sppd'] = 'SPPD';
$route['sppd/add'] = 'SPPD/add';
$route['sppd/add/process'] = 'SPPD/process_add';
$route['sppd/edit/(:num)'] = 'SPPD/edit/$1';
$route['sppd/edit/(:num)/process'] = 'SPPD/process_edit/$1';

$route['surat'] = 'Surat';
$route['surat/process'] = 'Surat/process_surat';

$route['config/wablas'] = 'Config/WABlas';
$route['config/wablas/update'] = 'Config/WABlas/process_update';

$route['document/download/(:any)'] = 'Surat/download_surat/$1';

$route['penugasan'] = 'Penugasan';
$route['penugasan/add'] = 'Penugasan/add';
$route['penugasan/add/process'] = 'Penugasan/process_add';
$route['penugasan/edit/(:num)'] = 'Penugasan/edit/$1';
$route['penugasan/edit/(:num)/process'] = 'Penugasan/process_edit/$1';
$route['penugasan/delete'] = 'Penugasan/process_delete';
$route['penugasan/release'] = 'Penugasan/process_release';
$route['penugasan/collect'] = 'Penugasan/collect';
$route['penugasan/collect/process'] = 'Penugasan/process_collect';
$route['penugasan/verify'] = 'Penugasan/process_verify';
$route['penugasan/reject'] = 'Penugasan/process_reject';
$route['penugasan/reject/process'] = 'Penugasan/process_reject_task';
$route['penugasan/history'] = 'Penugasan/history';
$route['penugasan/process'] = 'Penugasan/process_task';
$route['penugasan/detail/(:num)'] = 'Penugasan/detail/$1';
$route['penugasan/detail/(:num)/delete'] = 'Penugasan/process_delete';
$route['penugasan/detail_status'] = 'Penugasan/detail_status';
$route['penugasan/switch'] = 'Penugasan/switch_task';
$route['penugasan/switch/process'] = 'Penugasan/process_switch_task';
$route['penugasan/villages'] = 'Penugasan/select_villages';

$route['penugasan/(:any)'] = 'Penugasan/index/$1';
$route['penugasan/(:any)/datatables'] = 'Penugasan/datatables/$1';
$route['penugasan/(:any)/add'] = 'Penugasan/add/$1';
$route['penugasan/(:any)/add/process'] = 'Penugasan/process_add/$1';
$route['penugasan/(:any)/edit/(:num)'] = 'Penugasan/edit/$1/$2';
$route['penugasan/(:any)/edit/(:num)/process'] = 'Penugasan/process_edit/$1/$2';
$route['penugasan/(:any)/delete'] = 'Penugasan/process_delete/$1';
$route['penugasan/(:any)/release'] = 'Penugasan/process_release/$1';
$route['penugasan/(:any)/collect'] = 'Penugasan/collect/$1';
$route['penugasan/(:any)/collect/process'] = 'Penugasan/process_collect/$1';
$route['penugasan/(:any)/verify'] = 'Penugasan/process_verify/$1';
$route['penugasan/(:any)/reject'] = 'Penugasan/process_reject/$1';
$route['penugasan/(:any)/reject/process'] = 'Penugasan/process_reject_task/$1';
$route['penugasan/(:any)/detail/(:num)'] = 'Penugasan/detail/$1/$2';
$route['penugasan/(:any)/detail/(:num)/delete'] = 'Penugasan/process_delete/$1';
$route['penugasan/(:any)/detail_status'] = 'Penugasan/detail_status/$1';
$route['penugasan/(:any)/switch'] = 'Penugasan/switch_task/$1';
$route['penugasan/(:any)/switch/process'] = 'Penugasan/process_switch_task/$1';
$route['penugasan/(:any)/history'] = 'Penugasan/history/$1';
$route['penugasan/(:any)/process'] = 'Penugasan/process_task';

$route['integration'] = 'Integration';
$route['integration/update'] = 'Integration/process_update';

$route['laporan/absensi'] = 'Laporan/Absensi';

$route['event'] = 'Event';
$route['event/add'] = 'Event/add';
$route['event/add/process'] = 'Event/process_add';
$route['event/delete'] = 'Event/process_delete';
$route['event/edit/(:num)'] = 'Event/edit/$1';
$route['event/edit/(:num)/process'] = 'Event/process_edit/$1';
$route['event/show_qr_code'] = 'Event/show_qr_code';
$route['event/show_qr_code/(:num)/print'] = 'Event/print_qr_code/$1';
$route['event/list_of_attendance'] = 'Event/list_of_attendance';
$route['event/list_of_attendance/print/(:num)'] = 'Event/print_list_of_attendance/$1';

$route['history/deletes'] = 'History/deletes';

$route['api/listofattendance/scan'] = 'API/ListOfAttendance/scan';
$route['api/listofattendance/process'] = 'API/ListOfAttendance/process';

$route['api/task/get'] = 'API/Tasks/get';

$route['cronjobs/sync/bpd'] = 'Cronjobs/sync_bpd';
$route['cronjobs/sync/aparatdesa'] = 'Cronjobs/sync_aparatdesa';
$route['cronjobs/sync/task/bpd'] = 'Cronjobs/sync_task_bpd';
$route['cronjobs/sync/task/aparatdesa'] = 'Cronjobs/sync_task_aparatdesa';

$route['cronjobs/task/delete'] = 'Cronjobs/delete_task';

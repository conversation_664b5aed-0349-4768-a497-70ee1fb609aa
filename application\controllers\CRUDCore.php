<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property CI_DB_query_builder $db
 * @property CI_Upload $upload
 * @property CRUD $crud
 */
class CRUDCore extends CI_Controller
{
    public function index($feature)
    {
        $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');

        $data = array();
        $data['title'] = ucwords($feature);
        $data['content'] = $this->crud->showContents($feature, 'index');
        $data['element'] = $this->crud->getElements(getContents($feature, 'index'));

        return $this->load->view('master', $data);
    }

    public function add($feature)
    {
        $data = array();
        $data['title'] = 'Tambah ' . ucwords($feature);
        $data['feature'] = $feature;
        $data['content'] = $this->crud->showContents($feature, 'add');
        $data['element'] = $this->crud->getElements(getContents($feature, 'add'));

        return $this->load->view('master', $data);
    }

    public function process_add($feature)
    {
        try {
            $this->db->trans_begin();

            $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');
            $models = $feature . 's';

            $config = array();
            $config['upload_path'] = './uploads';
            $config['encrypt_name'] = true;
            $config['allowed_types'] = '*';

            $this->load->library('upload', $config);

            $fields = getFieldParameter($feature, 'add');
            $client_fields = array();

            $insert = array();
            foreach ($_FILES as $key => $value) {
                if ($value['name'] == null) continue;

                if ($this->upload->do_upload($key)) {
                    $insert[$key] = $this->upload->data('file_name');
                } else {
                    return JSONResponseDefault('FAILED', $this->upload->display_errors('', ''));
                }
            }

            foreach ($_POST as $key => $value) {
                if (str_contains($key, 'validated')) {
                    $col = explode('_', $key);

                    if (count($col) == 2) {
                        $validate = $this->$models->get(array($col[1] => getPost($key)));

                        if ($validate->num_rows() > 0) {
                            return JSONResponseDefault('FAILED', ucwords($col[1]) . ' yang anda masukkan telah terdata');
                        }

                        $insert[$col[1]] = getPost($key);
                    } elseif (count($col) > 2) {
                        if ($col[0] == 'validated' && $col[1] == 'with') {
                            $columns = isset($col[2]) ? $col[2] : null;
                            $table = isset($col[4]) ? $col[4] : null;
                            $parameter = isset($col[5]) ? $col[5] : null;
                            $statement = isset($col[6]) ? $col[6] : null;
                            $options = isset($col[8]) ? $col[8] : null;

                            $get = $this->db->get_where($table, array('id' => getPost($parameter)))->row();

                            switch ($statement) {
                                case 'mustlessthanorsame':
                                    if ($get != null && $get->{$columns} < $value) {
                                        $message = ucwords($col[7]) . " harus kurang dari atau sama dengan jumlah $columns yang tersedia";

                                        return JSONResponseDefault('FAILED', $message);
                                    } elseif ($get != null && $options == 'andthen' && $col[9] == 'update') {
                                        $upd = array();
                                        $upd[$columns] = $get->{$columns} - getPost($key);

                                        $this->db->set($upd)->where('id', $get->id)->update($table);
                                    }

                                    break;
                            }

                            $insert[$col[7]] = getPost($key);
                        } else if ($col[0] == 'required' && $col[1] == 'validated') {
                            $columns = $col[2];

                            $validate = $this->$models->get(array($columns => getPost($key)));

                            if ($validate->num_rows() > 0) {
                                return JSONResponseDefault('FAILED', ucwords($columns) . ' yang anda masukkan telah terdata');
                            }

                            $insert[$columns] = getPost($key);
                        }
                    }
                } else if (str_contains($key, 'required')) {
                    $col = explode('_', $key);

                    array_shift($col);
                    $columns = implode('_', $col);

                    $val = getPost($key);
                    if ($val === null || $val === '') {
                        return JSONResponseDefault('FAILED', ucwords(str_replace('_', ' ', $columns)) . ' harus diisi');
                    } else if (is_numeric($val)) {
                        if ($val < 0) {
                            return JSONResponseDefault('FAILED', ucwords(str_replace('_', ' ', $columns)) . ' tidak boleh bernilai negatif');
                        }
                    } else if (is_array($val)) {
                        if (count($val) == 0) {
                            return JSONResponseDefault('FAILED', ucwords(str_replace('_', ' ', $columns)) . ' harus dipilih');
                        }
                    } else {
                        // Check if field should be numeric
                        $numeric_fields = ['nik', 'nomor_surat', 'nomor_keputusan', 'nomor_diterapkan', 'nomor_kesepakatan', 'no_kk', 'phonenumber', 'rekening'];
                        if (in_array($columns, $numeric_fields) && !is_numeric($val)) {
                            return JSONResponseDefault('FAILED', ucwords(str_replace('_', ' ', $columns)) . ' harus berupa angka');
                        }
                    }

                    if (str_contains($columns, 'validated')) {
                        $col = explode('_', $columns);

                        $insert[$col[1]] = getPost($key);
                    } else {
                        $insert[$columns] = getPost($key);
                    }
                } else {
                    if (getPost($key) != null) {
                        if (str_contains($key, 'password')) {
                            $insert[$key] = password_hash(getPost($key), PASSWORD_DEFAULT);
                        } else {
                            if (str_contains($key, 'validated')) {
                                $col = explode('_', $key);

                                $insert[$col[1]] = getPost($key);
                            } else {
                                if (is_array(getPost($key))) {
                                    $insert[$key] = implode(',', getPost($key));
                                } else {
                                    $insert[$key] = getPost($key);
                                }
                            }
                        }
                    }
                }

                $client_fields[] = $key;
            }

            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->$models->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                $this->db->trans_rollback();

                return JSONResponseDefault('FAILED', 'Server sedang sibuk! Silahkan coba lagi nanti');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', 'Server sedang sibuk! Silahkan coba lagi nanti');
        }
    }

    public function edit($feature, $id)
    {
        $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');
        $models = $feature . 's';

        $get = $this->$models->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return redirect(base_url('master/' . $feature));
        }

        $data = array();
        $data['title'] = 'Ubah ' . ucwords($feature);
        $data['feature'] = $feature;
        $data['content'] = $this->crud->showContents($feature, 'edit');
        $data['element'] = $this->crud->getElements(getContents($feature, 'edit', $get->row()));

        return $this->load->view('master', $data);
    }

    public function detail($feature, $id)
    {
        $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');
        $models = $feature . 's';

        $get = $this->$models->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return redirect(base_url('master/' . $feature));
        }

        $data = array();
        $data['title'] = 'Detail ' . ucwords($feature);
        $data['feature'] = $feature;
        $data['content'] = $this->crud->showContents($feature, 'detail');
        $data['element'] = $this->crud->getElements(getContents($feature, 'detail', $get->row()));

        return $this->load->view('master', $data);
    }

    public function process_edit($feature, $id)
    {
        $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');
        $models = $feature . 's';

        $get = $this->$models->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        $config = array();
        $config['upload_path'] = './uploads';
        $config['encrypt_name'] = true;
        $config['allowed_types'] = '*';

        $this->load->library('upload', $config);

        $update = array();
        foreach ($_FILES as $key => $value) {
            if ($value['name'] == null) continue;

            if ($this->upload->do_upload($key)) {
                $update[$key] = $this->upload->data('file_name');
            } else {
                return JSONResponseDefault('FAILED', $this->upload->display_errors('', ''));
            }
        }

        foreach ($_POST as $key => $value) {
            if (str_contains($key, 'validated')) {
                $col = explode('_', $key);

                if (count($col) == 2) {
                    if (getPost($key) != $row->{$col[1]}) {
                        $validate = $this->$models->get(array($col[1] => getPost($key)));

                        if ($validate->num_rows() > 0) {
                            return JSONResponseDefault('FAILED', ucwords($col[1]) . ' yang anda masukkan telah terdata');
                        }

                        $update[$col[1]] = getPost($key);
                    }
                } elseif (count($col) > 2) {
                    if ($col[0] == 'validated' && $col[1] == 'with') {
                        $columns = $col[2];
                        $table = $col[4];
                        $parameter = $col[5];
                        $statement = $col[6];
                        $options = $col[8];

                        $get = $this->db->get_where($table, array('id' => getPost($parameter)))->row();

                        switch ($statement) {
                            case 'mustlessthanorsame':
                                if ($get != null && $get->{$columns} < $value) {
                                    $message = ucwords($col[7]) . " harus kurang dari atau sama dengan jumlah $columns yang tersedia";

                                    return JSONResponseDefault('FAILED', $message);
                                } elseif ($get != null && $options == 'andthen' && $col[9] == 'update') {
                                    $upd = array();
                                    $upd[$columns] = $get->{$columns} - getPost($key);

                                    $this->db->set($upd)->where('id', $get->id)->update($table);
                                }

                                break;
                        }

                        $update[$col[7]] = getPost($key);
                    } else if ($col[0] == 'required' && $col[1] == 'validated') {
                        $columns = $col[2];

                        if (getPost($key) != $row->{$columns}) {
                            $validate = $this->$models->get(array($columns => getPost($key)));

                            if ($validate->num_rows() > 0) {
                                return JSONResponseDefault('FAILED', ucwords($columns) . ' yang anda masukkan telah terdata');
                            }

                            $update[$columns] = getPost($key);
                        }
                    }
                }
            } else if (str_contains($key, 'required')) {
                $col = explode('_', $key);

                array_shift($col);
                $columns = implode('_', $col);

                $val = getPost($key);
                if ($val === null || $val === '') {
                    return JSONResponseDefault('FAILED', ucwords(str_replace('_', ' ', $columns)) . ' harus diisi');
                } else if (is_numeric($val)) {
                    if ($val < 0) {
                        return JSONResponseDefault('FAILED', ucwords(str_replace('_', ' ', $columns)) . ' tidak boleh bernilai negatif');
                    }
                } else if (is_array($val)) {
                    if (count($val) == 0) {
                        return JSONResponseDefault('FAILED', ucwords(str_replace('_', ' ', $columns)) . ' harus dipilih');
                    }
                } else {
                    // Check if field should be numeric
                    $numeric_fields = ['nik', 'nomor_surat', 'nomor_keputusan', 'nomor_diterapkan', 'nomor_kesepakatan', 'no_kk', 'phonenumber', 'rekening'];
                    if (in_array($columns, $numeric_fields) && !is_numeric($val)) {
                        return JSONResponseDefault('FAILED', ucwords(str_replace('_', ' ', $columns)) . ' harus berupa angka');
                    }
                }

                if (str_contains($columns, 'validated')) {
                    $col = explode('_', $columns);

                    $update[$col[1]] = getPost($key);
                } else {
                    $update[$columns] = getPost($key);
                }
            } else {
                if (getPost($key) != null) {
                    if (str_contains($key, 'password')) {
                        $update[$key] = password_hash(getPost($key), PASSWORD_DEFAULT);
                    } else {
                        if (str_contains($key, 'validated')) {
                            $col = explode('_', $key);

                            $update[$col[1]] = getPost($key);
                        } else {
                            if (is_array(getPost($key))) {
                                $update[$key] = implode(',', getPost($key));
                            } else {
                                $update[$key] = getPost($key);
                            }
                        }
                    }
                }
            }
        }

        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->$models->update(array('id' => $id), $update);

        return JSONResponseDefault('OK', 'Data berhasil diubah');
    }

    public function process_delete($feature)
    {
        $id = getPost('id');

        $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');
        $models = $feature . 's';

        $get = $this->$models->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $this->$models->delete(array('id' => $id));

        insertLogactivity('delete', "Menghapus data $feature", $get->row());

        return JSONResponseDefault('OK', 'Data berhasil dihapus');
    }
}

<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Kecamatans $kecamatans
 */
class Kecamatan extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Kecamatans', 'kecamatans');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Kecamatan';
        $data['content'] = 'master/kecamatan/index';
        $data['kecamatan'] = $this->kecamatans->result(array(
            'role' => 'Kecamatan'
        ));

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Tambah Kecamatan';
        $data['content'] = 'master/kecamatan/add';
        $data['kabupaten'] = GidesHelper::getKabKota()->data ?? array();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $name = getPost('name');
        $username = getPost('username');
        $password = getPost('password');
        $kabupaten = getPost('kabupaten');
        $kecamatan = getPost('kecamatan');

        if ($name == null) {
            return JSONResponseDefault('FAILED', 'Nama tidak boleh kosong!');
        } else if ($username == null) {
            return JSONResponseDefault('FAILED', 'Username tidak boleh kosong!');
        } else if ($password == null) {
            return JSONResponseDefault('FAILED', 'Password tidak boleh kosong!');
        } else if ($kabupaten == null) {
            return JSONResponseDefault('FAILED', 'Kabupaten tidak boleh kosong!');
        } else if ($kecamatan == null) {
            return JSONResponseDefault('FAILED', 'Kecamatan tidak boleh kosong!');
        } else {
            if (strpos($username, ' ') !== false) {
                return JSONResponseDefault('FAILED', 'Username tidak boleh mengandung spasi!');
            }
        }

        $get = $this->kecamatans->get(array(
            'username' => $username
        ));

        if ($get->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Username sudah digunakan!');
        }

        $kabkota = GidesHelper::getKabKota()->data ?? array();

        $nama_kabkota = null;
        foreach ($kabkota as $key => $value) {
            if ($value->id_kabkota == $kabupaten) {
                $nama_kabkota = $value->nama_kabkota;
            }
        }

        $g_kecamatan = GidesHelper::getKecamatan($kabupaten)->data ?? array();

        $nama_kecamatan = null;
        foreach ($g_kecamatan as $key => $value) {
            if ($value->id_kecamatan == $kecamatan) {
                $nama_kecamatan = $value->nama_kecamatan;
            }
        }

        $get_kecamatan = $this->kecamatans->get(array(
            'kecamatanid' => $kecamatan,
            'role' => 'Kecamatan'
        ));

        if ($get_kecamatan->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Kecamatan sudah ada!');
        }

        $insert = array();
        $insert['name'] = $name;
        $insert['username'] = $username;
        $insert['password'] = password_hash($password, PASSWORD_DEFAULT);
        $insert['role'] = 'Kecamatan';
        $insert['kabkotaid'] = $kabupaten;
        $insert['kabkotaname'] = $nama_kabkota;
        $insert['kecamatanid'] = $kecamatan;
        $insert['kecamatanname'] = $nama_kecamatan;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $this->kecamatans->insert($insert);

        return JSONResponseDefault('OK', 'Berhasil menambahkan Kecamatan!');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $get = $this->kecamatans->get(array(
            'id' => $id,
            'role' => 'Kecamatan'
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('master/kecamatan'));
        }

        $data = array();
        $data['title'] = 'Edit Kecamatan';
        $data['content'] = 'master/kecamatan/edit';
        $data['kabupaten'] = GidesHelper::getKabKota()->data ?? array();
        $data['kecamatan'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $get = $this->kecamatans->get(array(
            'id' => $id,
            'role' => 'Kecamatan'
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Kecamatan tidak ditemukan!');
        }

        $name = getPost('name');
        $username = getPost('username');
        $password = getPost('password');
        $kabupaten = getPost('kabupaten');
        $kecamatan = getPost('kecamatan');

        if ($name == null) {
            return JSONResponseDefault('FAILED', 'Nama tidak boleh kosong!');
        } else if ($username == null) {
            return JSONResponseDefault('FAILED', 'Username tidak boleh kosong!');
        } else if ($kabupaten == null) {
            return JSONResponseDefault('FAILED', 'Kabupaten tidak boleh kosong!');
        } else if ($kecamatan == null) {
            return JSONResponseDefault('FAILED', 'Kecamatan tidak boleh kosong!');
        } else {
            if (strpos($username, ' ') !== false) {
                return JSONResponseDefault('FAILED', 'Username tidak boleh mengandung spasi!');
            }
        }

        $get = $this->kecamatans->get(array(
            'username' => $username,
            'id !=' => $id
        ));

        if ($get->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Username sudah digunakan!');
        }

        $kabkota = GidesHelper::getKabKota()->data ?? array();

        $nama_kabkota = null;
        foreach ($kabkota as $key => $value) {
            if ($value->id_kabkota == $kabupaten) {
                $nama_kabkota = $value->nama_kabkota;
            }
        }

        $g_kecamatan = GidesHelper::getKecamatan($kabupaten)->data ?? array();

        $nama_kecamatan = null;
        foreach ($g_kecamatan as $key => $value) {
            if ($value->id_kecamatan == $kecamatan) {
                $nama_kecamatan = $value->nama_kecamatan;
            }
        }

        $get_kecamatan = $this->kecamatans->get(array(
            'kecamatanid' => $kecamatan,
            'id !=' => $id,
            'role' => 'Kecamatan'
        ));

        if ($get_kecamatan->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Kecamatan sudah ada!');
        }

        $update = array();
        $update['name'] = $name;
        $update['username'] = $username;
        $update['kabkotaid'] = $kabupaten;
        $update['kabkotaname'] = $nama_kabkota;
        $update['kecamatanid'] = $kecamatan;
        $update['kecamatanname'] = $nama_kecamatan;

        if (!empty($password)) {
            $update['password'] = password_hash($password, PASSWORD_DEFAULT);
        }

        $this->kecamatans->update(array(
            'id' => $id,
        ), $update);

        return JSONResponseDefault('OK', 'Berhasil mengubah Kecamatan!');
    }
}

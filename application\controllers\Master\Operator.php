<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Operators $operators
 * @property Jabatans $jabatans
 * @property Task $task
 * @property CI_DB_query_builder|CI_DB_mysqli_driver $db
 */
class Operator extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Operators', 'operators');
        $this->load->model('Jabatans', 'jabatans');
        $this->load->model('Task', 'task');
    }

    public function index()
    {
        $kecamatanid = getGet('kecamatanid');
        $villageid = getGet('villageid');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $currentuser = getCurrentUser();

        $where = array(
            'a.role' => 'Operator'
        );

        if (!isKecamatan()) {
            if (!isPMD()) {
                $where['a.desaid'] = $currentuser->desaid;
            } else {
                $where['a.kabkotaid'] = $currentuser->kabkotaid;
            }
        } else {
            $where['a.kecamatanid'] = $currentuser->kecamatanid;
        }

        if ($kecamatanid != null) {
            $where['a.kecamatanid'] = $kecamatanid;
        }

        if ($villageid != null) {
            $villageuser = getCurrentUser($villageid);
            $where['a.desaid'] = $villageuser->desaid;
        }

        $data = array();
        $data['title'] = 'Operator Desa';
        $data['content'] = 'master/operator/index';
        $data['operators'] = $this->operators->select('a.*, IFNULL(b.name, a.position) as positionname')
            ->join('jabatan b', 'a.positionid = b.id', 'LEFT')
            ->result($where);

        if (isKecamatan()) {
            $data['village'] = $this->operators->result(array(
                'a.role' => 'Villages',
                'a.kecamatanid' => $currentuser->kecamatanid
            ));
        } else if (isPMD()) {
            $data['kecamatan'] = $this->operators->result(array(
                'a.role' => 'Kecamatan',
                'a.kabkotaid' => $currentuser->kabkotaid
            ));
            $data['village'] = $this->operators->result(array(
                'a.role' => 'Villages',
                'a.kabkotaid' => $currentuser->kabkotaid
            ));
        }

        $data['kecamatanid'] = $kecamatanid;
        $data['villageid'] = $villageid;

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Tambah Operator Desa';
        $data['content'] = 'master/operator/add';
        $data['position'] = $this->jabatans->order_by('ordering', 'ASC')->result(array(
            'type' => 'Operator Desa'
        ));

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $currentuser = getCurrentUser();

        $name = getPost('name');
        $phonenumber = getPost('phonenumber');
        $position = getPost('position');
        $username = getPost('username');
        $password = getPost('password');
        $position = getPost('position');
        $nik = getPost('nik');
        $gender = getPost('gender');

        if ($name == null) {
            return JSONResponseDefault('FAILED', 'Nama tidak boleh kosong!');
        } else if ($phonenumber == null) {
            return JSONResponseDefault('FAILED', 'Nomor Handphone tidak boleh kosong!');
        } else if (!is_numeric($phonenumber)) {
            return JSONResponseDefault('FAILED', 'Nomor Handphone harus berupa angka!');
        } else if ($position == null) {
            return JSONResponseDefault('FAILED', 'Jabatan tidak boleh kosong!');
        } else if ($username == null) {
            return JSONResponseDefault('FAILED', 'Username tidak boleh kosong!');
        } else if ($password == null) {
            return JSONResponseDefault('FAILED', 'Password tidak boleh kosong!');
        } else if ($nik == null) {
            return JSONResponseDefault('FAILED', 'NIK tidak boleh kosong!');
        } else if (!is_numeric($nik)) {
            return JSONResponseDefault('FAILED', 'NIK harus berupa angka!');
        } else if (strlen($nik) != 16) {
            return JSONResponseDefault('FAILED', 'NIK harus 16 digit!');
        } else if ($gender == null) {
            return JSONResponseDefault('FAILED', 'Jenis Kelamin tidak boleh kosong!');
        } else if (!in_array($gender, array('Laki-laki', 'Perempuan'))) {
            return JSONResponseDefault('FAILED', 'Jenis Kelamin tidak valid!');
        } else {
            if (strpos($username, ' ') !== false) {
                return JSONResponseDefault('FAILED', 'Username tidak boleh mengandung spasi!');
            }
        }

        $get = $this->operators->get(array(
            'username' => $username
        ));

        if ($get->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Username sudah digunakan!');
        }

        $validate_nik = $this->operators->get(array(
            'nik' => $nik,
        ));

        if ($validate_nik->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'NIK sudah digunakan!');
        }

        $get_position = $this->jabatans->get(array(
            'id' => $position
        ));

        if ($get_position->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Jabatan tidak ditemukan!');
        }

        $insert = array();
        $insert['name'] = $name;
        $insert['username'] = $username;
        $insert['password'] = password_hash($password, PASSWORD_DEFAULT);
        $insert['role'] = 'Operator';
        $insert['kabkotaid'] = $currentuser->kabkotaid;
        $insert['kabkotaname'] = $currentuser->kabkotaname;
        $insert['kecamatanid'] = $currentuser->kecamatanid;
        $insert['kecamatanname'] = $currentuser->kecamatanname;
        $insert['desaid'] = $currentuser->desaid;
        $insert['desaname'] = $currentuser->desaname;
        $insert['position'] = $get_position->row()->name;
        $insert['positionid'] = $position;
        $insert['phonenumber'] = $phonenumber;
        $insert['nik'] = $nik;
        $insert['gender'] = $gender;

        $this->operators->insert($insert);
        $operatorid = $this->db->insert_id();

        $kabupaten = $currentuser->kabkotaid;
        $kecamatan = $currentuser->kecamatanid;

        $task = $this->task->select('a.*')
            ->join('msusers b', 'b.id = a.createdby')
            ->result(array(
                'a.parentid' => null,
                'a.is_release' => 1,
                'a.deadline' => date('Y-m-d'),
                "((b.role = 'DPMD' AND b.kabkotaid = $kabupaten) OR (b.role = 'Kecamatan' AND b.kecamatanid = $kecamatan)) =" => true,
                'a.taskfor' => 'Operator Desa',
                'a.to' => $position,
            ));

        foreach ($task as $key => $value) {
            $insert = array();
            $insert['deadline'] = $value->deadline;
            $insert['task'] = $value->task;
            $insert['to'] = $operatorid;
            $insert['description'] = $value->description;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = $value->createdby;
            $insert['is_release'] = 1;
            $insert['status'] = 'Process';
            $insert['taskfor'] = 'Operator Desa';
            $insert['parentid'] = $value->id;
            $insert['starttask'] = $value->starttask;

            $this->task->insert($insert);
        }

        return JSONResponseDefault('OK', 'Berhasil menambahkan Operator Desa!');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $get = $this->operators->get(array(
            'id' => $id,
            'role' => 'Operator'
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('master/village'));
        }

        $data = array();
        $data['title'] = 'Edit Operator Desa';
        $data['content'] = 'master/operator/edit';
        $data['operators'] = $get->row();
        $data['position'] = $this->jabatans->order_by('ordering', 'ASC')->result(array(
            'type' => 'Operator Desa'
        ));

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $name = getPost('name');
        $phonenumber = getPost('phonenumber');
        $position = getPost('position');
        $username = getPost('username');
        $password = getPost('password');
        $position = getPost('position');
        $nik = getPost('nik');
        $gender = getPost('gender');

        if ($name == null) {
            return JSONResponseDefault('FAILED', 'Nama tidak boleh kosong!');
        } else if ($phonenumber == null) {
            return JSONResponseDefault('FAILED', 'Nomor Handphone tidak boleh kosong!');
        } else if (!is_numeric($phonenumber)) {
            return JSONResponseDefault('FAILED', 'Nomor Handphone harus berupa angka!');
        } else if ($position == null) {
            return JSONResponseDefault('FAILED', 'Jabatan tidak boleh kosong!');
        } else if ($username == null) {
            return JSONResponseDefault('FAILED', 'Username tidak boleh kosong!');
        } else if ($nik == null) {
            return JSONResponseDefault('FAILED', 'NIK tidak boleh kosong!');
        } else if (!is_numeric($nik)) {
            return JSONResponseDefault('FAILED', 'NIK harus berupa angka!');
        } else if (strlen($nik) != 16) {
            return JSONResponseDefault('FAILED', 'NIK harus 16 digit!');
        } else if ($gender == null) {
            return JSONResponseDefault('FAILED', 'Jenis Kelamin tidak boleh kosong!');
        } else if (!in_array($gender, array('Laki-laki', 'Perempuan'))) {
            return JSONResponseDefault('FAILED', 'Jenis Kelamin tidak valid!');
        } else {
            if (strpos($username, ' ') !== false) {
                return JSONResponseDefault('FAILED', 'Username tidak boleh mengandung spasi!');
            }
        }

        $get_operator = $this->operators->get(array(
            'id' => $id
        ));

        if ($get_operator->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Operator tidak ditemukan!');
        }

        $row_operator = $get_operator->row();

        $get = $this->operators->get(array(
            'username' => $username,
            'id !=' => $id
        ));

        if ($get->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Username sudah digunakan!');
        }

        $validate_nik = $this->operators->get(array(
            'nik' => $nik,
            'id !=' => $id
        ));

        if ($validate_nik->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'NIK sudah digunakan!');
        }

        $get_position = $this->jabatans->get(array(
            'id' => $position
        ));

        if ($get_position->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Jabatan tidak ditemukan!');
        }

        $update = array();
        $update['name'] = $name;
        $update['username'] = $username;
        $update['role'] = 'Operator';
        $update['position'] = $get_position->row()->name;
        $update['positionid'] = $position;
        $update['phonenumber'] = $phonenumber;
        $update['nik'] = $nik;
        $update['gender'] = $gender;

        if ($password != null) {
            $update['password'] = password_hash($password, PASSWORD_DEFAULT);
        }

        $this->operators->update(array('id' => $id), $update);

        if ($row_operator->positionid != $position) {
            $task = $this->task->select('a.*')
                ->join('msusers b', 'b.id = a.createdby')
                ->result(array(
                    'a.parentid' => null,
                    'a.is_release' => 1,
                    'a.deadline' => date('Y-m-d'),
                    "((b.role = 'DPMD' AND b.kabkotaid = $row_operator->kabkotaid) OR (b.role = 'Kecamatan' AND b.kecamatanid = $row_operator->kecamatanid)) =" => true,
                    'a.taskfor' => 'Operator Desa',
                    'a.to' => $position,
                ));

            foreach ($task as $key => $value) {
                $insert = array();
                $insert['deadline'] = $value->deadline;
                $insert['task'] = $value->task;
                $insert['to'] = $id;
                $insert['description'] = $value->description;
                $insert['createddate'] = getCurrentDate();
                $insert['createdby'] = $value->createdby;
                $insert['is_release'] = 1;
                $insert['status'] = 'Process';
                $insert['taskfor'] = 'Operator Desa';
                $insert['parentid'] = $value->id;
                $insert['starttask'] = $value->starttask;

                $this->task->insert($insert);
            }

            $task = $this->task->select('a.*')
                ->join('msusers b', 'b.id = a.createdby')
                ->result(array(
                    'a.parentid' => null,
                    'a.is_release' => 1,
                    'a.deadline' => date('Y-m-d'),
                    "(b.role = 'DPMD' OR b.role = 'Kecamatan') =" => true,
                    "(b.kabkotaid = $row_operator->kabkotaid OR b.kecamatanid = $row_operator->kecamatanid) =" => true,
                    'a.taskfor' => 'Operator Desa',
                    'a.to' => $row_operator->positionid,
                ));

            foreach ($task as $key => $value) {
                $this->task->delete(array(
                    'parentid' => $value->id,
                    'to' => $id
                ));

                insertLogactivity('delete', 'Menghapus tugas ' . $value->task);
            }
        }

        return JSONResponseDefault('OK', 'Berhasil mengubah Operator Desa!');
    }

    private function get_report($apikey, $nik)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://siades.id/api/thirdparty/report/attendance',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => 'apikey=' . $apikey . '&nik=' . $nik,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded',
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);

        return json_decode($response);
    }

    public function monitoring()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $id = getPost('id');

        $get = $this->operators->select('a.*')
            ->get(array(
                'a.id' => $id,
            ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Operator tidak ditemukan!');
        }

        $row = $get->row();

        $villages = $this->operators->get(array(
            'a.role' => 'Villages',
            'a.desaid' => $row->desaid
        ));

        if ($villages->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Desa belum memiliki data!');
        }

        $village = $villages->row();
        $apikey = $village->apikey;

        $report = $this->get_report($apikey, $row->nik);

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('master/operator/monitoring', array(
                'detail' => isset($report->status) && $report->status ? $report->data : array()
            ), true)
        ));
    }
}

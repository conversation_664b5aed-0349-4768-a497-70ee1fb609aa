<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Superadmins $admins
 */
class SuperAdmin extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Superadmins', 'admins');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Super Admin';
        $data['content'] = 'master/admin/index';
        $data['admin'] = $this->admins->result(array(
            'role' => 'Admin'
        ));

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Tambah Super Admin';
        $data['content'] = 'master/admin/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        $name = getPost('name');
        $username = getPost('username');
        $password = getPost('password');

        if ($name == null || trim($name) == '') {
            return JSONResponseDefault('FAILED', 'Nama tidak boleh kosong');
        } else if ($username == null || trim($username) == '') {
            return JSONResponseDefault('FAILED', 'Username tidak boleh kosong');
        } else if ($password == null) {
            return JSONResponseDefault('FAILED', 'Password tidak boleh kosong');
        }

        $get = $this->admins->get(array(
            'username' => $username
        ));

        if ($get->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Username yang anda masukkan telah terdaftar');
        }

        $insert = array();
        $insert['name'] = $name;
        $insert['username'] = $username;
        $insert['password'] = password_hash($password, PASSWORD_DEFAULT);
        $insert['role'] = 'Admin';

        $this->admins->insert($insert);

        return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $get = $this->admins->get(array(
            'id' => $id,
            'role' => 'Admin'
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('master/admin'));
        }

        $row = $get->row();

        $data = array();
        $data['title'] = 'Ubah Super Admin';
        $data['content'] = 'master/admin/edit';
        $data['admin'] = $row;

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu!');
        }

        $get = $this->admins->get(array(
            'id' => $id,
            'role' => 'Admin'
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        $name = getPost('name');
        $username = getPost('username');
        $password = getPost('password');

        if ($name == null || trim($name) == '') {
            return JSONResponseDefault('FAILED', 'Nama tidak boleh kosong');
        } else if ($username == null || trim($username) == '') {
            return JSONResponseDefault('FAILED', 'Username tidak boleh kosong');
        }

        if ($row->username != $username) {
            $get = $this->admins->get(array(
                'username' => $username
            ));

            if ($get->num_rows() > 0) {
                return JSONResponseDefault('FAILED', 'Username yang anda masukkan telah terdaftar');
            }
        }

        $update = array();
        $update['name'] = $name;
        $update['username'] = $username;

        if ($password != null) {
            $update['password'] = password_hash($password, PASSWORD_DEFAULT);
        }

        $this->admins->update(array('id' => $id), $update);

        return JSONResponseDefault('OK', 'Data berhasil diubah');
    }
}

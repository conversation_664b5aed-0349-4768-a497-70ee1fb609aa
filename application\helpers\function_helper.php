<?php
defined('BASEPATH') or die('No direct script access allowed!');

function JSONResponse($data = array())
{
    $CI = &get_instance();
    $CI->output->set_content_type('application/json');

    echo json_encode($data);
}

function JSONResponseDefault($result, $message)
{
    return JSONResponse(array(
        'RESULT' => $result,
        'MESSAGE' => $message
    ));
}

function getPost($index, $default = null)
{
    $CI = &get_instance();

    if ($CI->input->post($index)) {
        if (!is_array($CI->input->post($index))) {
            return trim($CI->input->post($index));
        } else {
            return $CI->input->post($index);
        }
    }

    return $default;
}

function getGet($index, $default = null)
{
    $CI = &get_instance();

    if ($CI->input->get($index)) {
        return $CI->input->get($index);
    }

    return $default;
}

function getModels($models = array())
{
    $ci = &get_instance();

    foreach ($models as $key => $value) {
        $ci->load->model($key, $value);
    }
}

function getSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->userdata($index);
}

function setSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->set_userdata($index);
}

function hasSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->has_userdata($index);
}

function unsetSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->unset_userdata($index);
}

function destroySession()
{
    $CI = &get_instance();

    return $CI->session->sess_destroy();
}

function getCurrentIdUser()
{
    return getSessionValue('USERID');
}

function getCurrentUser($id = null)
{
    $CI = &get_instance();

    return $CI->db->get_where('msusers', array('id' => $id ?? getCurrentIdUser()))->row();
}

function isLogin()
{
    return getSessionValue('ISLOGIN');
}

if (!function_exists('str_contains')) {
    function str_contains($needle, $haystack)
    {
        return stripos($needle, $haystack) !== false;
    }
}

function IDR($nominal, $digit = 0, $pemisah = '.', $rupiah = ',')
{
    return number_format($nominal, $digit, $pemisah, $rupiah);
}

function getCurrentDate($format = 'Y-m-d H:i:s')
{
    date_default_timezone_set("Asia/Makassar");
    return date($format);
}

function getContents($feature, $name, $row = null, $other = array())
{
    $output = array();
    $ci = &get_instance();

    switch ($feature) {
        case 'jabatan':
            if ($name == 'index') {
                $output = array(
                    'table' => [
                        'name' => 'Nama',
                        'ordering' => 'Urutan',
                        'type' => 'Tipe Jabatan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/jabatan/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'fa fa-edit',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.name}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'fa fa-trash',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'method' => 'result'
                    ]
                );
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Nama Jabatan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_name',
                                    'placeholder' => 'Masukkan Nama Jabatan',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Urutan Jabatan',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'required_ordering',
                                    'placeholder' => 'Masukkan Urutan Jabatan',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tipe Jabatan',
                                    'type' => 'select',
                                    'variable' => 'required_type',
                                    'required' => true,
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            [
                                                'id' => 'Operator Desa',
                                                'name' => 'Operator Desa'
                                            ],
                                            [
                                                'id' => 'BPD',
                                                'name' => 'BPD'
                                            ],
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->type : null
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }

            break;

        case 'suratkeluar':
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Tanggal Surat',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'date',
                                    'required' => true,
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->date)),
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor Surat',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nomorsurat',
                                    'placeholder' => 'Masukkan Nomor Surat',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Hal dan Isi Singkat',
                                    'type' => 'textarea',
                                    'variable' => 'required_isisurat',
                                    'placeholder' => 'Masukkan Hal dan Isi Singkat',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tujuan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_tujuan',
                                    'placeholder' => 'Masukkan Tujuan',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Keterangan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Masukkan Keterangan',
                                ],
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'suratmasuk':
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Tanggal Surat',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'date',
                                    'required' => true,
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->date)),
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor Surat',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nomorsurat',
                                    'placeholder' => 'Masukkan Nomor Surat',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Instansi Pengirim',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_instansi',
                                    'placeholder' => 'Masukkan Instansi Pengirim',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Hal dan Isi Singkat',
                                    'type' => 'textarea',
                                    'variable' => 'required_isisurat',
                                    'placeholder' => 'Masukkan Hal dan Isi Singkat',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Keterangan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Masukkan Keterangan',
                                ],
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'guestbook':
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Tamu',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Tanggal',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'date',
                                    'required' => true,
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->date)),
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nama',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama',
                                    'placeholder' => 'Masukkan Nama',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Jabatan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_jabatan',
                                    'placeholder' => 'Masukkan Jabatan',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Instansi',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_instansi',
                                    'placeholder' => 'Masukkan Instansi',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Alamat',
                                    'type' => 'textarea',
                                    'variable' => 'required_alamat',
                                    'placeholder' => 'Masukkan Alamat',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Keperluan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_keperluan',
                                    'placeholder' => 'Masukkan Keperluan',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Foto',
                                    'type' => 'input',
                                    'input_type' => 'file',
                                    'variable' => 'foto',
                                    'required' => $name == 'add' ? true : false,
                                    'attr' => [
                                        'accept' => 'image/*'
                                    ],
                                    'helpertext' =>  $name == 'edit' ? '*Kosongkan jika tidak ingin diubah' : null,
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'aspirasimasyarakat':
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Aspirasi Masyarakat',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser(),
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Tanggal',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'date',
                                    'required' => true,
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->date)),
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nama/Lembaga Pihak Penyampai Aspirasi',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama',
                                    'placeholder' => 'Masukkan Nama/Lembaga Pihak Penyampai Aspirasi',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Aspirasi yang Disampaikan',
                                    'type' => 'textarea',
                                    'variable' => 'required_aspirasi',
                                    'placeholder' => 'Masukkan Aspirasi yang Disampaikan',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Tindak Lanjut',
                                    'type' => 'textarea',
                                    'variable' => 'required_tindaklanjut',
                                    'placeholder' => 'Masukkan Tindak Lanjut',
                                    'required' => true,
                                ],
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'daftarhadir':
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Daftar Hadir',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser(),
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nama',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama',
                                    'placeholder' => 'Masukkan Nama',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Jenis Kelamin',
                                    'type' => 'select',
                                    'variable' => 'required_gender',
                                    'required' => true,
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            [
                                                'id' => 'Laki - laki',
                                                'name' => 'Laki - laki'
                                            ],
                                            [
                                                'id' => 'Perempuan',
                                                'name' => 'Perempuan'
                                            ]
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->gender : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Jabatan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_jabatan',
                                    'placeholder' => 'Masukkan Jabatan',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Instansi / Desa',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_instansi',
                                    'placeholder' => 'Masukkan Instansi / Desa',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Alamat',
                                    'type' => 'textarea',
                                    'variable' => 'required_alamat',
                                    'placeholder' => 'Masukkan Alamat',
                                    'required' => true,
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'anggotabpd':
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Anggota BPD',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Nama Lengkap',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama',
                                    'placeholder' => 'Masukkan Nama Lengkap',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Alamat',
                                    'type' => 'textarea',
                                    'variable' => 'required_address',
                                    'placeholder' => 'Masukkan Alamat',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'NIK',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_validated_nip',
                                    'placeholder' => 'Masukkan NIK',
                                    'required' => true,
                                    'attr' => [
                                        'minlength' => 16,
                                        'maxlength' => 16,
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Jenis Kelamin',
                                    'type' => 'select',
                                    'variable' => 'required_jenis_kelamin',
                                    'required' => true,
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            [
                                                'id' => 'Laki - laki',
                                                'name' => 'Laki - laki'
                                            ],
                                            [
                                                'id' => 'Perempuan',
                                                'name' => 'Perempuan'
                                            ]
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->jenis_kelamin : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tempat Lahir',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_tempat_lahir',
                                    'placeholder' => 'Masukkan Tempat Lahir',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal Lahir',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'required_tanggal_lahir',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Agama',
                                    'type' => 'select',
                                    'variable' => 'required_agama',
                                    'required' => true,
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            [
                                                'id' => 'Islam',
                                                'name' => 'Islam'
                                            ],
                                            [
                                                'id' => 'Kristen',
                                                'name' => 'Kristen'
                                            ],
                                            [
                                                'id' => 'Katolik',
                                                'name' => 'Katolik'
                                            ],
                                            [
                                                'id' => 'Hindu',
                                                'name' => 'Hindu'
                                            ],
                                            [
                                                'id' => 'Budha',
                                                'name' => 'Budha'
                                            ],
                                            [
                                                'id' => 'Kong Hu Cu',
                                                'name' => 'Kong Hu Cu'
                                            ],
                                            [
                                                'id' => 'Lainnya',
                                                'name' => 'Lainnya'
                                            ]
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->jenis_kelamin : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Jabatan',
                                    'type' => 'select',
                                    'variable' => 'required_positionid',
                                    'required' => true,
                                    'defaultvalue' => [
                                        'data' => $ci->db->order_by('ordering', 'ASC')->get_where('jabatan', array(
                                            'type' => 'BPD'
                                        ))->result(),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->positionid : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Pendidikan Terakhir',
                                    'type' => 'select',
                                    'variable' => 'required_pendidikan_terakhir',
                                    'required' => true,
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            [
                                                'id' => 'TK/PAUD',
                                                'name' => 'TK/PAUD'
                                            ],
                                            [
                                                'id' => 'SD/MI',
                                                'name' => 'SD/MI'
                                            ],
                                            [
                                                'id' => 'SMP/MTs',
                                                'name' => 'SMP/MTs'
                                            ],
                                            [
                                                'id' => 'SMA/SMK',
                                                'name' => 'SMA/SMK'
                                            ],
                                            [
                                                'id' => 'S1',
                                                'name' => 'S1'
                                            ],
                                            [
                                                'id' => 'S2',
                                                'name' => 'S2'
                                            ],
                                            [
                                                'id' => 'S3',
                                                'name' => 'S3'
                                            ],
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->jenis_kelamin : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor Pengangkatan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nomor_pengangkatan',
                                    'placeholder' => 'Masukkan Nomor Pengangkatan',
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal Pengangkatan',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_pengangkatan',
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor Pemberhentian',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nomor_pemberhentian',
                                    'placeholder' => 'Masukkan Nomor Pemberhentian',
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal Pemberhentian',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_pemberhentian',
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Status',
                                    'type' => 'select',
                                    'variable' => 'keterangan',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            [
                                                'id' => 'Aktif',
                                                'name' => 'Aktif'
                                            ],
                                            [
                                                'id' => 'Sedang Proses PAW',
                                                'name' => 'Sedang Proses PAW'
                                            ],
                                            [
                                                'id' => 'Telah Berhenti',
                                                'name' => 'Telah Berhenti'
                                            ]
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->keterangan : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Foto',
                                    'type' => 'input',
                                    'input_type' => 'file',
                                    'variable' => 'foto',
                                    'required' => $name == 'add' ? true : false,
                                    'attr' => [
                                        'accept' => 'image/*'
                                    ],
                                    'helpertext' =>  $name == 'edit' ? '*Kosongkan jika tidak ingin diubah' : null,
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'keputusanbpd':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Keputusan BPD',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Tanggal Keputusan',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'date',
                                    'required' => true,
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->date)),
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor Keputusan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nomor',
                                    'placeholder' => 'Masukkan Nomor Keputusan',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tentang',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_tentang',
                                    'placeholder' => 'Masukkan Tentang',
                                    'required' => true
                                ],
                            ],
                        ],
                        [
                            'label' => 'Dokumen',
                            'type' => 'input',
                            'input_type' => 'file',
                            'variable' => 'document',
                            'required' => $name == 'add' ? true : false,
                            'attr' => [
                                'accept' => '.pdf,.docx,.doc,.xls,.xlsx'
                            ],
                            'helpertext' =>  $name == 'edit' ? '*Kosongkan jika tidak ingin diubah' : null,
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'peraturandesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Peraturan Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor Peraturan Desa',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nomor',
                                    'placeholder' => 'Masukkan Nomor Peraturan Desa',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal Peraturan Desa',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'date',
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->createddate)),
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tentang',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_tentang',
                                    'placeholder' => 'Masukkan Tentang',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Masukkan Keterangan',
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Uraian Singkat',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_uraiansingkat',
                                    'placeholder' => 'Masukkan Uraian Singkat',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor Kesepakatan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nomorkesepakatan',
                                    'placeholder' => 'Masukkan Nomor Kesepakatan',
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal Kesepakatan',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggalkesepakatan',
                                ],
                            ]
                        ],
                        [
                            'label' => 'Dokumen',
                            'type' => 'input',
                            'input_type' => 'file',
                            'variable' => 'document',
                            'required' => $name == 'add' ? true : false,
                            'attr' => [
                                'accept' => '.pdf,.docx,.doc,.xls,.xlsx'
                            ],
                            'helpertext' =>  $name == 'edit' ? '*Kosongkan jika tidak ingin diubah' : null,
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'keputusankepaladesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'index') {
                $output = array(
                    'title' => 'Keputusan Kepala Desa',
                    'table' => [
                        'date' => 'Tanggal Keputusan',
                        'nomor' => 'Nomor Keputusan',
                        'tentang' => 'Tentang',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('uploads/${table.value.document}'),
                                    'class' => 'btn btn-light btn-sm',
                                    'icon' => 'fa fa-download',
                                    'text' => 'Unduh',
                                    'attr' => [
                                        'target' => '_blank'
                                    ]
                                ],
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/keputusankepaladesa/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'fa fa-edit',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.nomor}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'fa fa-trash',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'method' => 'result',
                        'order_by' => [
                            'a.date' => 'DESC'
                        ]
                    ]
                );
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Keputusan Kepala Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tanggal Keputusan',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'date',
                                    'required' => true,
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->date)),
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Nomor Keputusan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nomor',
                                    'placeholder' => 'Masukkan Nomor Keputusan',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tentang',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_tentang',
                                    'placeholder' => 'Masukkan Tentang',
                                    'required' => true
                                ],
                            ]
                        ],
                        [
                            'label' => 'Dokumen',
                            'type' => 'input',
                            'input_type' => 'file',
                            'variable' => 'document',
                            'required' => $name == 'add' ? true : false,
                            'attr' => [
                                'accept' => '.pdf,.doc,.docx,.xls,.xlsx'
                            ],
                            'helpertext' =>  $name == 'edit' ? '*Kosongkan jika tidak ingin diubah' : null,
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }

            break;

        case 'lembarandesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'index') {
                $output = array(
                    'title' => 'Lembaran Desa & Berita Desa',
                    'table' => [
                        'jenis' => 'Jenis Peraturan',
                        'nomor_ditetapkan' => 'Nomor Ditetapkan',
                        'tanggal_ditetapkan' => 'Tanggal Ditetapkan',
                        'tentang' => 'Tentang',
                        'tanggal_diundangkan' => 'Tanggal Diundangkan',
                        'nomor_diundangkan' => 'Nomor Diundangkan',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/lembarandesa/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'fa fa-edit',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.tentang}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'fa fa-trash',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'method' => 'result',
                        'order_by' => [
                            'a.tanggal_ditetapkan' => 'DESC'
                        ]
                    ]
                );
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Lembaran Desa & Berita Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Jenis Peraturan di Desa',
                                    'type' => 'select',
                                    'variable' => 'required_jenis',
                                    'required' => true,
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'Peraturan Desa', 'name' => 'Peraturan Desa'],
                                            ['id' => 'Peraturan Bersama Kepala Desa', 'name' => 'Peraturan Bersama Kepala Desa'],
                                            ['id' => 'Peraturan Kepala Desa', 'name' => 'Peraturan Kepala Desa'],
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->jenis : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tentang',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_tentang',
                                    'placeholder' => 'Masukkan Tentang',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor (Ditetapkan)',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nomor_ditetapkan',
                                    'placeholder' => 'Masukkan Nomor Ditetapkan',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal (Ditetapkan)',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'required_tanggal_ditetapkan',
                                    'required' => true,
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->tanggal_ditetapkan)),
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor (Diundangkan)',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nomor_diundangkan',
                                    'placeholder' => 'Masukkan Nomor Diundangkan',
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal (Diundangkan)',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_diundangkan',
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Masukkan Keterangan',
                                ],
                            ]
                        ],
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }

            break;

        case 'aparatdesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'index') {
                $output = array(
                    'title' => 'Aparat Pemerintah Desa',
                    'table' => [
                        'nama' => 'Nama',
                        'niap' => 'NIAP',
                        'nip' => 'NIP',
                        'gender' => 'L/P',
                        'jabatan' => 'Jabatan',
                        'pendidikan_terakhir' => 'Pendidikan Terakhir',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/aparatdesa/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'fa fa-edit',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.nama}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'fa fa-trash',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'method' => 'result',
                        'order_by' => [
                            'a.nama' => 'ASC'
                        ]
                    ]
                );
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Aparat Pemerintah Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nama',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama',
                                    'placeholder' => 'Masukkan Nama',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'NIAP',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'niap',
                                    'placeholder' => 'Masukkan NIAP',
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'NIP',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nip',
                                    'placeholder' => 'Masukkan NIP',
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Jenis Kelamin',
                                    'type' => 'select',
                                    'variable' => 'required_gender',
                                    'required' => true,
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'L', 'name' => 'Laki-laki'],
                                            ['id' => 'P', 'name' => 'Perempuan'],
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->gender : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Jabatan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_jabatan',
                                    'placeholder' => 'Masukkan Jabatan',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Pangkat/Golongan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'pangkat_gol',
                                    'placeholder' => 'Masukkan Pangkat/Golongan',
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Pendidikan Terakhir',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_pendidikan_terakhir',
                                    'placeholder' => 'Masukkan Pendidikan Terakhir',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Agama',
                                    'type' => 'select',
                                    'variable' => 'agama',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'Islam', 'name' => 'Islam'],
                                            ['id' => 'Kristen', 'name' => 'Kristen'],
                                            ['id' => 'Katholik', 'name' => 'Katholik'],
                                            ['id' => 'Hindu', 'name' => 'Hindu'],
                                            ['id' => 'Budha', 'name' => 'Budha'],
                                            ['id' => 'Konghucu', 'name' => 'Konghucu'],
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->agama : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tempat, Tanggal Lahir',
                                    'type' => 'row',
                                    'data' => [
                                        [
                                            'parent_class' => 'col-md-6',
                                            'type' => 'input',
                                            'input_type' => 'text',
                                            'variable' => 'ttl',
                                            'placeholder' => 'cth: Ciamis, 02-02-1990'
                                        ],
                                        [
                                            'parent_class' => 'col-md-6',
                                            'type' => 'input',
                                            'input_type' => 'date',
                                            'variable' => 'tanggallahir',
                                        ]
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'SK (Pengangkatan & Pemberhentian)',
                                    'type' => 'row',
                                    'data' => [
                                        [
                                            'parent_class' => 'col-md-6',
                                            'label' => 'No. SK Pengangkatan',
                                            'type' => 'input',
                                            'input_type' => 'text',
                                            'variable' => 'no_sk_pengangkatan'
                                        ],
                                        [
                                            'parent_class' => 'col-md-6',
                                            'label' => 'Tanggal SK Pengangkatan',
                                            'type' => 'input',
                                            'input_type' => 'date',
                                            'variable' => 'tanggal_sk_pengangkatan'
                                        ],
                                        [
                                            'parent_class' => 'col-md-6',
                                            'label' => 'No. SK Pemberhentian',
                                            'type' => 'input',
                                            'input_type' => 'text',
                                            'variable' => 'no_sk_pemberhentian'
                                        ],
                                        [
                                            'parent_class' => 'col-md-6',
                                            'label' => 'Tanggal SK Pemberhentian',
                                            'type' => 'input',
                                            'input_type' => 'date',
                                            'variable' => 'tanggal_sk_pemberhentian'
                                        ]
                                    ]
                                ],
                            ]
                        ],
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }

            break;

        case 'agenda':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Agenda',
                    'table' => [
                        'tanggal' => 'Tanggal',
                        'jenis' => 'Jenis',
                        'nomor_surat' => 'Nomor Surat',
                        'asal' => 'Asal',
                        'tujuan' => 'Tujuan',
                        'perihal' => 'Perihal',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/agenda/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'fa fa-edit',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.nomor_surat}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'fa fa-trash',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'method' => 'result',
                        'order_by' => [
                            'a.tanggal' => 'DESC'
                        ]
                    ]
                );
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Agenda',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tanggal',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'required_tanggal',
                                    'required' => true,
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->tanggal)),
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Jenis',
                                    'type' => 'select',
                                    'variable' => 'required_jenis',
                                    'required' => true,
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'Masuk', 'name' => 'Masuk'],
                                            ['id' => 'Keluar', 'name' => 'Keluar'],
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->jenis : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Nomor Surat',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nomor_surat',
                                    'placeholder' => 'Masukkan Nomor Surat',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Asal (untuk Masuk)',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'asal',
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tujuan (untuk Keluar)',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'tujuan',
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Perihal/Isi Singkat',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_perihal',
                                    'placeholder' => 'Masukkan Perihal/Isi Singkat',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                ],
                            ]
                        ],
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }

            break;

        case 'ekspedisi':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Ekspedisi',
                    'table' => [
                        'tanggal' => 'Tanggal',
                        'nomor_surat' => 'Nomor Surat',
                        'tujuan' => 'Tujuan',
                        'perihal' => 'Perihal',
                        'penerima' => 'Penerima',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/ekspedisi/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'fa fa-edit',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.nomor_surat}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'fa fa-trash',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'method' => 'result',
                        'order_by' => [
                            'tanggal' => 'DESC'
                        ]
                    ]
                );
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Ekspedisi',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tanggal',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'required_tanggal',
                                    'required' => true,
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->tanggal)),
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Nomor Surat',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nomor_surat',
                                    'placeholder' => 'Masukkan Nomor Surat',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tujuan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_tujuan',
                                    'placeholder' => 'Masukkan Tujuan',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Perihal',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'perihal',
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Penerima',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'penerima',
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                ],
                            ]
                        ],
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }

            break;

        case 'inventarisdesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Inventaris & Kekayaan Desa',
                    'table' => [
                        'jenis_barang' => 'Jenis Barang',
                        'merk_type' => 'Merk/Type',
                        'tahun_perolehan' => 'Tahun Perolehan',
                        'asal' => 'Asal Usul',
                        'harga' => 'Harga (Rp)',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/inventarisdesa/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'fa fa-edit',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.jenis_barang}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'fa fa-trash',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'method' => 'result',
                        'order_by' => [
                            'a.tahun_perolehan' => 'DESC'
                        ]
                    ]
                );
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Inventaris & Kekayaan Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Jenis Barang',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_jenis_barang',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Kode Barang',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'kode_barang',
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Register',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'register',
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Merk/Type',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'merk_type',
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Ukuran/CC',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'ukuran_cc',
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Bahan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'bahan',
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tahun Perolehan',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'required_tahun_perolehan',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Asal Usul',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'asal',
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Harga (Rp)',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'harga',
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                ],
                            ]
                        ],
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }

            break;

        case 'tanahkasdesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Tanah Kas Desa',
                    'table' => [
                        'letak' => 'Letak',
                        'luas' => 'Luas (m2)',
                        'kelas' => 'Kelas',
                        'status' => 'Status Hak',
                        'penggunaan' => 'Penggunaan',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/tanahkasdesa/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'fa fa-edit',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.letak}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'fa fa-trash',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'method' => 'result',
                        'order_by' => [
                            'a.id' => 'DESC'
                        ]
                    ]
                );
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Tanah Kas Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Letak',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_letak',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Luas (m2)',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'luas',
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Kelas',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'kelas',
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Status Hak',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'status',
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Penggunaan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'penggunaan',
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                ],
                            ]
                        ],
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }

            break;

        case 'tanahdidesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Tanah di Desa',
                    'table' => [
                        'letak' => 'Letak',
                        'luas' => 'Luas (m2)',
                        'kelas' => 'Kelas',
                        'status' => 'Status Hak',
                        'pemilik' => 'Pemilik',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/tanahdidesa/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'fa fa-edit',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.letak}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'fa fa-trash',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'method' => 'result',
                        'order_by' => [
                            'a.id' => 'DESC'
                        ]
                    ]
                );
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Tanah di Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Letak',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_letak',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Luas (m2)',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'luas',
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Kelas',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'kelas',
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Status Hak',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'status',
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Pemilik',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'pemilik',
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                ],
                            ]
                        ],
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }

            break;

        case 'indukpenduduk':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Induk Penduduk',
                    'table' => [
                        'nama' => 'Nama',
                        'nik' => 'NIK',
                        'kk' => 'No. KK',
                        'jenis_kelamin' => 'L/P',
                        'alamat' => 'Alamat',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/indukpenduduk/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.nama' => 'ASC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Induk Penduduk',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-6', 'label' => 'Nama', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_nama', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'NIK', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_nik', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'No. KK', 'type' => 'input', 'input_type' => 'text', 'variable' => 'kk'],
                            ['parent_class' => 'col-md-4', 'label' => 'Tempat Lahir', 'type' => 'input', 'input_type' => 'text', 'variable' => 'tempat_lahir'],
                            ['parent_class' => 'col-md-4', 'label' => 'Tanggal Lahir', 'type' => 'input', 'input_type' => 'date', 'variable' => 'tanggal_lahir'],
                            ['parent_class' => 'col-md-4', 'label' => 'Jenis Kelamin', 'type' => 'select', 'variable' => 'required_jenis_kelamin', 'required' => true, 'defaultvalue' => ['data' => json_decode(json_encode([
                                ['id' => 'L', 'name' => 'Laki-laki'],
                                ['id' => 'P', 'name' => 'Perempuan']
                            ])), 'value' => 'id', 'text' => 'name', 'selected' => $row != null ? $row->jenis_kelamin : null]],
                            ['parent_class' => 'col-md-4', 'label' => 'Agama', 'type' => 'input', 'input_type' => 'text', 'variable' => 'agama'],
                            ['parent_class' => 'col-md-4', 'label' => 'Pendidikan', 'type' => 'input', 'input_type' => 'text', 'variable' => 'pendidikan'],
                            ['parent_class' => 'col-md-4', 'label' => 'Pekerjaan', 'type' => 'input', 'input_type' => 'text', 'variable' => 'pekerjaan'],
                            ['parent_class' => 'col-md-4', 'label' => 'Kewarganegaraan', 'type' => 'input', 'input_type' => 'text', 'variable' => 'kewarganegaraan'],
                            ['parent_class' => 'col-md-8', 'label' => 'Alamat', 'type' => 'input', 'input_type' => 'text', 'variable' => 'alamat'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan'],
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'mutasipenduduk':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Mutasi Penduduk Desa',
                    'table' => [
                        'nama' => 'Nama',
                        'jenis_mutasi' => 'Jenis Mutasi',
                        'tanggal' => 'Tanggal',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/mutasipenduduk/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tanggal' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Mutasi Penduduk Desa',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-6', 'label' => 'Nama', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_nama', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Tempat Lahir', 'type' => 'input', 'input_type' => 'text', 'variable' => 'tempat_lahir'],
                            ['parent_class' => 'col-md-3', 'label' => 'Tanggal Lahir', 'type' => 'input', 'input_type' => 'date', 'variable' => 'tanggal_lahir'],
                            ['parent_class' => 'col-md-3', 'label' => 'Jenis Kelamin', 'type' => 'select', 'variable' => 'required_jenis_kelamin', 'required' => true, 'defaultvalue' => ['data' => json_decode(json_encode([
                                ['id' => 'L', 'name' => 'Laki-laki'],
                                ['id' => 'P', 'name' => 'Perempuan']
                            ])), 'value' => 'id', 'text' => 'name', 'selected' => $row != null ? $row->jenis_kelamin : null]],
                            ['parent_class' => 'col-md-3', 'label' => 'Kewarganegaraan', 'type' => 'input', 'input_type' => 'text', 'variable' => 'kewarganegaraan'],
                            ['parent_class' => 'col-md-6', 'label' => 'Jenis Mutasi', 'type' => 'select', 'variable' => 'required_jenis_mutasi', 'required' => true, 'defaultvalue' => ['data' => json_decode(json_encode([
                                ['id' => 'Datang', 'name' => 'Datang'],
                                ['id' => 'Pindah', 'name' => 'Pindah'],
                                ['id' => 'Meninggal', 'name' => 'Meninggal']
                            ])), 'value' => 'id', 'text' => 'name', 'selected' => $row != null ? $row->jenis_mutasi : null]],
                            ['parent_class' => 'col-md-4', 'label' => 'Alamat Asal', 'type' => 'input', 'input_type' => 'text', 'variable' => 'alamat_asal'],
                            ['parent_class' => 'col-md-4', 'label' => 'Alamat Tujuan', 'type' => 'input', 'input_type' => 'text', 'variable' => 'alamat_tujuan'],
                            ['parent_class' => 'col-md-4', 'label' => 'Tanggal', 'type' => 'input', 'input_type' => 'date', 'variable' => 'required_tanggal', 'required' => true],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'rekappenduduk':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Rekapitulasi Jumlah Penduduk',
                    'table' => [
                        'periode' => 'Periode',
                        'jumlah_l' => 'Laki-laki',
                        'jumlah_p' => 'Perempuan',
                        'jumlah_total' => 'Total',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/rekappenduduk/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.periode}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.periode' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Rekapitulasi Jumlah Penduduk',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-4', 'label' => 'Periode', 'type' => 'input', 'input_type' => 'month', 'variable' => 'required_periode', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Jumlah Laki-laki', 'type' => 'input', 'input_type' => 'number', 'variable' => 'required_jumlah_l', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Jumlah Perempuan', 'type' => 'input', 'input_type' => 'number', 'variable' => 'required_jumlah_p', 'required' => true],
                            ['parent_class' => 'col-md-2', 'label' => 'Total', 'type' => 'input', 'input_type' => 'number', 'variable' => 'jumlah_total'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'penduduksementara':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Penduduk Sementara',
                    'table' => [
                        'nama' => 'Nama',
                        'jenis_kelamin' => 'L/P',
                        'asal' => 'Asal',
                        'tujuan' => 'Tujuan',
                        'tanggal_datang' => 'Tgl Datang',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/penduduksementara/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tanggal_datang' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Penduduk Sementara',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-4', 'label' => 'Nama', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_nama', 'required' => true],
                            ['parent_class' => 'col-md-2', 'label' => 'L/P', 'type' => 'select', 'variable' => 'required_jenis_kelamin', 'required' => true, 'defaultvalue' => ['data' => json_decode(json_encode([
                                ['id' => 'L', 'name' => 'Laki-laki'],
                                ['id' => 'P', 'name' => 'Perempuan']
                            ])), 'value' => 'id', 'text' => 'name', 'selected' => $row != null ? $row->jenis_kelamin : null]],
                            ['parent_class' => 'col-md-3', 'label' => 'Asal', 'type' => 'input', 'input_type' => 'text', 'variable' => 'asal'],
                            ['parent_class' => 'col-md-3', 'label' => 'Tujuan', 'type' => 'input', 'input_type' => 'text', 'variable' => 'tujuan'],
                            ['parent_class' => 'col-md-4', 'label' => 'Tanggal Datang', 'type' => 'input', 'input_type' => 'date', 'variable' => 'required_tanggal_datang', 'required' => true],
                            ['parent_class' => 'col-md-8', 'label' => 'Alamat', 'type' => 'input', 'input_type' => 'text', 'variable' => 'alamat'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'bukuktp':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku KTP',
                    'table' => [
                        'nama' => 'Nama',
                        'nik' => 'NIK',
                        'alamat' => 'Alamat',
                        'tanggal_pembuatan' => 'Tgl Pembuatan',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/bukuktp/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tanggal_pembuatan' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku KTP',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-4', 'label' => 'Nama', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_nama', 'required' => true],
                            ['parent_class' => 'col-md-4', 'label' => 'NIK', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_nik', 'required' => true],
                            ['parent_class' => 'col-md-4', 'label' => 'Tanggal Pembuatan', 'type' => 'input', 'input_type' => 'date', 'variable' => 'required_tanggal_pembuatan', 'required' => true],
                            ['parent_class' => 'col-md-12', 'label' => 'Alamat', 'type' => 'input', 'input_type' => 'text', 'variable' => 'alamat'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'bukukk':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku KK',
                    'table' => [
                        'kepala_keluarga' => 'Kepala Keluarga',
                        'no_kk' => 'No. KK',
                        'alamat' => 'Alamat',
                        'tanggal_pembuatan' => 'Tgl Pembuatan',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/bukukk/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.kepala_keluarga}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tanggal_pembuatan' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku KK',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-4', 'label' => 'Kepala Keluarga', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_kepala_keluarga', 'required' => true],
                            ['parent_class' => 'col-md-4', 'label' => 'No. KK', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_no_kk', 'required' => true],
                            ['parent_class' => 'col-md-4', 'label' => 'Tanggal Pembuatan', 'type' => 'input', 'input_type' => 'date', 'variable' => 'required_tanggal_pembuatan', 'required' => true],
                            ['parent_class' => 'col-md-12', 'label' => 'Alamat', 'type' => 'input', 'input_type' => 'text', 'variable' => 'alamat'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'rkpdes':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Rencana Kerja Pembangunan Desa (RKPDes)',
                    'table' => [
                        'tahun' => 'Tahun',
                        'program' => 'Program',
                        'kegiatan' => 'Kegiatan',
                        'lokasi' => 'Lokasi',
                        'sumber_dana' => 'Sumber Dana',
                        'pagu' => 'Pagu (Rp)',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/rkpdes/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.program}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tahun' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Rencana Kerja Pembangunan Desa (RKPDes)',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-3', 'label' => 'Tahun', 'type' => 'input', 'input_type' => 'number', 'variable' => 'required_tahun', 'required' => true],
                            ['parent_class' => 'col-md-4', 'label' => 'Program', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_program', 'required' => true],
                            ['parent_class' => 'col-md-5', 'label' => 'Kegiatan', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_kegiatan', 'required' => true],
                            ['parent_class' => 'col-md-6', 'label' => 'Lokasi', 'type' => 'input', 'input_type' => 'text', 'variable' => 'lokasi'],
                            ['parent_class' => 'col-md-3', 'label' => 'Sumber Dana', 'type' => 'input', 'input_type' => 'text', 'variable' => 'sumber_dana'],
                            ['parent_class' => 'col-md-3', 'label' => 'Pagu (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'pagu'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'kegiatanpembangunan':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Kegiatan Pembangunan',
                    'table' => [
                        'tanggal' => 'Tanggal',
                        'kegiatan' => 'Kegiatan',
                        'pelaksana' => 'Pelaksana',
                        'lokasi' => 'Lokasi',
                        'biaya' => 'Biaya (Rp)',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/kegiatanpembangunan/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.kegiatan}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tanggal' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Kegiatan Pembangunan',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-4', 'label' => 'Tanggal', 'type' => 'input', 'input_type' => 'date', 'variable' => 'required_tanggal', 'required' => true],
                            ['parent_class' => 'col-md-4', 'label' => 'Kegiatan', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_kegiatan', 'required' => true],
                            ['parent_class' => 'col-md-4', 'label' => 'Pelaksana', 'type' => 'input', 'input_type' => 'text', 'variable' => 'pelaksana'],
                            ['parent_class' => 'col-md-6', 'label' => 'Lokasi', 'type' => 'input', 'input_type' => 'text', 'variable' => 'lokasi'],
                            ['parent_class' => 'col-md-3', 'label' => 'Biaya (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'biaya'],
                            ['parent_class' => 'col-md-3', 'label' => 'Sumber Dana', 'type' => 'input', 'input_type' => 'text', 'variable' => 'sumber_dana'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'inventarisasihasilpembangunan':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Inventarisasi Hasil-hasil Pembangunan',
                    'table' => [
                        'nama_proyek' => 'Nama Proyek',
                        'lokasi' => 'Lokasi',
                        'volume' => 'Volume',
                        'biaya' => 'Biaya (Rp)',
                        'kondisi' => 'Kondisi',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/inventarisasihasilpembangunan/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama_proyek}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.id' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Inventarisasi Hasil-hasil Pembangunan',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-6', 'label' => 'Nama Proyek', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_nama_proyek', 'required' => true],
                            ['parent_class' => 'col-md-6', 'label' => 'Lokasi', 'type' => 'input', 'input_type' => 'text', 'variable' => 'lokasi'],
                            ['parent_class' => 'col-md-4', 'label' => 'Volume', 'type' => 'input', 'input_type' => 'text', 'variable' => 'volume'],
                            ['parent_class' => 'col-md-4', 'label' => 'Biaya (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'biaya'],
                            ['parent_class' => 'col-md-4', 'label' => 'Kondisi', 'type' => 'input', 'input_type' => 'text', 'variable' => 'kondisi'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'kaderpemberdayaan':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Kader Pendampingan dan Pemberdayaan Masyarakat',
                    'table' => [
                        'nama' => 'Nama',
                        'bidang' => 'Bidang',
                        'alamat' => 'Alamat',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/kaderpemberdayaan/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.id' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Kader Pendampingan dan Pemberdayaan Masyarakat',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-6', 'label' => 'Nama', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_nama', 'required' => true],
                            ['parent_class' => 'col-md-6', 'label' => 'Bidang', 'type' => 'input', 'input_type' => 'text', 'variable' => 'bidang'],
                            ['parent_class' => 'col-md-12', 'label' => 'Alamat', 'type' => 'input', 'input_type' => 'text', 'variable' => 'alamat'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'apbdesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku APB Desa',
                    'table' => [
                        'tahun' => 'Tahun',
                        'pendapatan' => 'Pendapatan (Rp)',
                        'belanja' => 'Belanja (Rp)',
                        'pembiayaan' => 'Pembiayaan (Rp)',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/apbdesa/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.tahun}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tahun' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku APB Desa',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-3', 'label' => 'Tahun', 'type' => 'input', 'input_type' => 'number', 'variable' => 'required_tahun', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Pendapatan (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'pendapatan'],
                            ['parent_class' => 'col-md-3', 'label' => 'Belanja (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'belanja'],
                            ['parent_class' => 'col-md-3', 'label' => 'Pembiayaan (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'pembiayaan'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'rabdesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Rencana Anggaran Biaya (RAB)',
                    'table' => [
                        'tahun' => 'Tahun',
                        'program' => 'Program',
                        'kegiatan' => 'Kegiatan',
                        'pagu' => 'Pagu (Rp)',
                        'rincian' => 'Rincian',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/rabdesa/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.program}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tahun' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku RAB',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-3', 'label' => 'Tahun', 'type' => 'input', 'input_type' => 'number', 'variable' => 'required_tahun', 'required' => true],
                            ['parent_class' => 'col-md-4', 'label' => 'Program', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_program', 'required' => true],
                            ['parent_class' => 'col-md-5', 'label' => 'Kegiatan', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_kegiatan', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Pagu (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'pagu'],
                            ['parent_class' => 'col-md-9', 'label' => 'Rincian', 'type' => 'textarea', 'variable' => 'rincian']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'kaspembantukegiatan':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Kas Pembantu Kegiatan',
                    'table' => [
                        'bidang' => 'Bidang',
                        'kegiatan' => 'Kegiatan',
                        'tanggal' => 'Tanggal',
                        'penerimaan' => 'Penerimaan (Rp)',
                        'pengeluaran' => 'Pengeluaran (Rp)',
                        'saldo' => 'Saldo (Rp)',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/kaspembantukegiatan/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.kegiatan}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tanggal' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Kas Pembantu Kegiatan',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-3', 'label' => 'Bidang', 'type' => 'input', 'input_type' => 'text', 'variable' => 'bidang'],
                            ['parent_class' => 'col-md-3', 'label' => 'Kegiatan', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_kegiatan', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Tanggal', 'type' => 'input', 'input_type' => 'date', 'variable' => 'required_tanggal', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'No Bukti', 'type' => 'input', 'input_type' => 'text', 'variable' => 'no_bukti'],
                            ['parent_class' => 'col-md-4', 'label' => 'Uraian', 'type' => 'input', 'input_type' => 'text', 'variable' => 'uraian'],
                            ['parent_class' => 'col-md-2', 'label' => 'Penerimaan (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'penerimaan'],
                            ['parent_class' => 'col-md-2', 'label' => 'Pengeluaran (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'pengeluaran'],
                            ['parent_class' => 'col-md-2', 'label' => 'Saldo (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'saldo'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'kasumum':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Kas Umum',
                    'table' => [
                        'tanggal' => 'Tanggal',
                        'kode_rekening' => 'Kode Rekening',
                        'uraian' => 'Uraian',
                        'penerimaan' => 'Penerimaan (Rp)',
                        'pengeluaran' => 'Pengeluaran (Rp)',
                        'saldo' => 'Saldo (Rp)',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/kasumum/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.kode_rekening}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tanggal' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Kas Umum',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-3', 'label' => 'Tanggal', 'type' => 'input', 'input_type' => 'date', 'variable' => 'required_tanggal', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Kode Rekening', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_kode_rekening', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Uraian', 'type' => 'input', 'input_type' => 'text', 'variable' => 'uraian'],
                            ['parent_class' => 'col-md-3', 'label' => 'No Bukti', 'type' => 'input', 'input_type' => 'text', 'variable' => 'no_bukti'],
                            ['parent_class' => 'col-md-3', 'label' => 'Penerimaan (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'penerimaan'],
                            ['parent_class' => 'col-md-3', 'label' => 'Pengeluaran (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'pengeluaran'],
                            ['parent_class' => 'col-md-3', 'label' => 'Saldo (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'saldo'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'kaspembantu':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Kas Pembantu',
                    'table' => [
                        'tanggal' => 'Tanggal',
                        'uraian' => 'Uraian',
                        'pemotongan' => 'Pemotongan (Rp)',
                        'penyetoran' => 'Penyetoran (Rp)',
                        'saldo' => 'Saldo (Rp)',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/kaspembantu/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.uraian}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tanggal' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Kas Pembantu',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-3', 'label' => 'Tanggal', 'type' => 'input', 'input_type' => 'date', 'variable' => 'required_tanggal', 'required' => true],
                            ['parent_class' => 'col-md-4', 'label' => 'Uraian', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_uraian', 'required' => true],
                            ['parent_class' => 'col-md-2', 'label' => 'Pemotongan (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'pemotongan'],
                            ['parent_class' => 'col-md-2', 'label' => 'Penyetoran (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'penyetoran'],
                            ['parent_class' => 'col-md-1', 'label' => 'Saldo (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'saldo'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'bankdesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Bank Desa',
                    'table' => [
                        'bulan' => 'Bulan',
                        'bank_cabang' => 'Bank/Cabang',
                        'rekening' => 'No Rekening',
                        'saldo_awal' => 'Saldo Awal',
                        'debet' => 'Debet',
                        'kredit' => 'Kredit',
                        'saldo' => 'Saldo',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/bankdesa/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.rekening}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.id' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Bank Desa',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-4', 'label' => 'Bulan', 'type' => 'input', 'input_type' => 'month', 'variable' => 'required_bulan', 'required' => true],
                            ['parent_class' => 'col-md-4', 'label' => 'Bank/Cabang', 'type' => 'input', 'input_type' => 'text', 'variable' => 'bank_cabang'],
                            ['parent_class' => 'col-md-4', 'label' => 'No Rekening', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_rekening', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Saldo Awal', 'type' => 'input', 'input_type' => 'number', 'variable' => 'saldo_awal'],
                            ['parent_class' => 'col-md-3', 'label' => 'Debet', 'type' => 'input', 'input_type' => 'number', 'variable' => 'debet'],
                            ['parent_class' => 'col-md-3', 'label' => 'Kredit', 'type' => 'input', 'input_type' => 'number', 'variable' => 'kredit'],
                            ['parent_class' => 'col-md-3', 'label' => 'Saldo', 'type' => 'input', 'input_type' => 'number', 'variable' => 'saldo'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'musyawarahdesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Musyawarah Desa',
                    'table' => [
                        'tanggal' => 'Tanggal',
                        'agenda' => 'Agenda',
                        'peserta' => 'Peserta',
                        'hasil_keputusan' => 'Hasil Keputusan',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/musyawarahdesa/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.agenda}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tanggal' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Musyawarah Desa',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-3', 'label' => 'Tanggal', 'type' => 'input', 'input_type' => 'date', 'variable' => 'required_tanggal', 'required' => true],
                            ['parent_class' => 'col-md-6', 'label' => 'Agenda', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_agenda', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Peserta', 'type' => 'input', 'input_type' => 'number', 'variable' => 'peserta'],
                            ['parent_class' => 'col-md-12', 'label' => 'Hasil Keputusan', 'type' => 'textarea', 'variable' => 'hasil_keputusan'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'lembagakemasyarakatan':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Lembaga Kemasyarakatan',
                    'table' => [
                        'nama_lembaga' => 'Nama Lembaga',
                        'jenis' => 'Jenis',
                        'pembina' => 'Pembina',
                        'alamat' => 'Alamat',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/lembagakemasyarakatan/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama_lembaga}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.id' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Lembaga Kemasyarakatan',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-4', 'label' => 'Nama Lembaga', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_nama_lembaga', 'required' => true],
                            ['parent_class' => 'col-md-4', 'label' => 'Jenis', 'type' => 'input', 'input_type' => 'text', 'variable' => 'jenis'],
                            ['parent_class' => 'col-md-4', 'label' => 'Pembina', 'type' => 'input', 'input_type' => 'text', 'variable' => 'pembina'],
                            ['parent_class' => 'col-md-12', 'label' => 'Alamat', 'type' => 'input', 'input_type' => 'text', 'variable' => 'alamat'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'kabupaten':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Data Kabupaten',
                    'table' => [
                        'nama_kabupaten' => 'Nama Kabupaten',
                        'kode_kabupaten' => 'Kode',
                        'provinsi' => 'Provinsi',
                        'alamat' => 'Alamat',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/kabupaten/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'a', 'href' => base_url('master/kabupaten/detail/${table.primary}'), 'class' => 'btn btn-info btn-sm', 'icon' => 'fa fa-eye'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama_kabupaten}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['id' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Data Kabupaten',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-6', 'label' => 'Nama Kabupaten', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_nama_kabupaten', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Kode Kabupaten', 'type' => 'input', 'input_type' => 'text', 'variable' => 'kode_kabupaten'],
                            ['parent_class' => 'col-md-3', 'label' => 'Provinsi', 'type' => 'input', 'input_type' => 'text', 'variable' => 'provinsi'],
                            ['parent_class' => 'col-md-12', 'label' => 'Alamat', 'type' => 'input', 'input_type' => 'text', 'variable' => 'alamat'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            if ($name == 'detail') {
                $output = array(
                    'title' => 'Detail Kabupaten',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-6', 'label' => 'Nama Kabupaten', 'type' => 'text', 'value' => $row->nama_kabupaten ?? ''],
                            ['parent_class' => 'col-md-3', 'label' => 'Kode Kabupaten', 'type' => 'text', 'value' => $row->kode_kabupaten ?? ''],
                            ['parent_class' => 'col-md-3', 'label' => 'Provinsi', 'type' => 'text', 'value' => $row->provinsi ?? ''],
                            ['parent_class' => 'col-md-12', 'label' => 'Alamat', 'type' => 'text', 'value' => $row->alamat ?? ''],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'text', 'value' => $row->keterangan ?? '']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)]
                    ]
                );
            }
            break;

        default:
            break;
    }

    return $output;
}

function getFieldParameter($feature, $name)
{
    $variable = array();
    foreach (getContents($feature, $name)['fields'] as $value) {
        if (isset($value['variable'])) {
            if (isset($value['required'])) {
                $variable[] = $value['variable'];
            }
        } else if (isset($value['data'])) {
            foreach ($value['data'] as $v) {
                if (isset($v['required'])) {
                    $variable[] = $v['variable'];
                }
            }
        }
    }

    return $variable;
}

function stringEncryption($action, $string)
{
    $output = false;

    $encrypt_method = 'AES-256-CBC'; // Default
    $secret_key = 'karpeldedvtech'; // Change the key!
    $secret_iv = 'owr216he890';  // Change the init vector!

    // hash
    $key = hash('sha256', $secret_key);

    // iv - encrypt method AES-256-CBC expects 16 bytes - else you will get a warning
    $iv = substr(hash('sha256', $secret_iv), 0, 16);

    if ($action == 'encrypt') {
        $output = openssl_encrypt($string, $encrypt_method, $key, 0, $iv);
        $output = base64_encode($output);
    } else if ($action == 'decrypt') {
        $output = openssl_decrypt(base64_decode($string), $encrypt_method, $key, 0, $iv);
    }

    return $output;
}

function validateDate($dateStr, $format)
{
    date_default_timezone_set('UTC');
    if ($dateStr != null) {
        $date = DateTime::createFromFormat($format, $dateStr);
        return $date && ($date->format($format) === $dateStr);
    } else {
        return false;
    }
}

function tgl_indo($tanggal)
{
    $bulan = array(
        'Januari',
        'Februari',
        'Maret',
        'April',
        'Mei',
        'Juni',
        'Juli',
        'Agustus',
        'September',
        'Oktober',
        'November',
        'Desember'
    );

    $pecahkan = explode('-', $tanggal);

    if (isset($pecahkan[0]) && isset($pecahkan[1]) && isset($pecahkan[2])) {
        return $pecahkan[2] . ' ' . $bulan[(int)$pecahkan[1] - 1] . ' ' . $pecahkan[0];
    } else {
        return date('d F Y', strtotime($tanggal));
    }
}

function isAdmin()
{
    return getSessionValue('ROLE') == 'Admin';
}

function isBPD()
{
    return getSessionValue('ROLE') == 'BPD';
}

function isVillages()
{
    return getSessionValue('ROLE') == 'Villages';
}

function isOperator()
{
    return getSessionValue('ROLE') == 'Operator';
}

function isKecamatan()
{
    return getSessionValue('ROLE') == 'Kecamatan';
}

function isPMD()
{
    return getSessionValue('ROLE') == 'DPMD';
}

function isKepalaDesa()
{
    return getSessionValue('ROLE') == 'Kepala Desa';
}

function getConfigWablas($key)
{
    $CI = &get_instance();

    $get = $CI->db->get('configwablas')->row();

    return $get != null ? $get->$key : null;
}

function isOnlineWABlas()
{
    $phonenumber = getConfigWablas('phonenumber');
    $token = getConfigWablas('apikey');
    $domain = getConfigWablas('domain');

    $curl = curl_init();

    curl_setopt($curl, CURLOPT_HTTPHEADER, array(
        "Authorization: $token",
        "url: $domain"
    ));
    curl_setopt($curl, CURLOPT_URL,  "https://phone.wablas.com/check-phone-number?phones=$phonenumber");
    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "GET");
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);

    $result = curl_exec($curl);
    curl_close($curl);

    return json_decode($result);
}

function sendWABlas($phonenumber, $message)
{
    $curl = curl_init();
    $secret_key = "wRD3WqKN";
    $token = getConfigWablas('apikey') . '.' . $secret_key;

    $data = [
        'phone' => $phonenumber,
        'message' => $message,
    ];

    $url_send = getConfigWablas('domain') . "api/send-message";

    curl_setopt($curl, CURLOPT_HTTPHEADER, array("Authorization: $token",));
    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($curl, CURLOPT_URL, $url_send);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);

    curl_exec($curl);
    curl_close($curl);
}

function getCurrentProfile()
{
    return base_url('assets/img/avatar/avatar-1.png');
}

function syncSiades($apikey, $type = 'BPD')
{
    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://siades.id/api/thirdparty/userapps',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => 'apikey=' . $apikey . '&type=' . $type,
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/x-www-form-urlencoded',
        ),
    ));

    $response = curl_exec($curl);
    curl_close($curl);

    return json_decode($response);
}

function asset_url($filename)
{
    $asseturl = "https://storage.googleapis.com/siades/$filename";

    $headers = get_headers($asseturl);
    $status = substr($headers[0], 9, 3);

    if ($status == '200') {
        return $asseturl;
    } else {
        return base_url('assets/img/avatars/1.png');
    }
}

function generateRandomString($length = 10)
{
    // create variable $characters with value '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    // create variable $charactersLength with value strlen($characters)
    $charactersLength = strlen($characters);
    // create variable $randomString with value ''
    $randomString = '';
    // create loop for generate random string
    for ($i = 0; $i < $length; $i++) {
        // create variable $randomString with value $characters[rand(0, $charactersLength - 1)]
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    // return $randomString
    return $randomString;
}

// create function to get day name indonesia    
function getDayIndo($date)
{
    $day = date('D', strtotime($date));

    switch ($day) {
        case 'Sun':
            $hari = "Minggu";
            break;

        case 'Mon':
            $hari = "Senin";
            break;

        case 'Tue':
            $hari = "Selasa";
            break;

        case 'Wed':
            $hari = "Rabu";
            break;

        case 'Thu':
            $hari = "Kamis";
            break;

        case 'Fri':
            $hari = "Jumat";
            break;

        case 'Sat':
            $hari = "Sabtu";
            break;

        default:
            $hari = "Tidak di ketahui";
            break;
    }

    return $hari;
}

function getKabupaten_Gides()
{
    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://desa.gides.id/master/select/kabupaten',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => 'provinsi=22',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/x-www-form-urlencoded',
        ),
    ));

    $response = curl_exec($curl);
    curl_close($curl);

    return $response;
}

function getKecamatan_Gides($kabupaten)
{
    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://desa.gides.id/master/select/kecamatan',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => 'kabupaten=' . $kabupaten,
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/x-www-form-urlencoded',
        ),
    ));

    $response = curl_exec($curl);
    curl_close($curl);

    return  $response;
}

function insertLogactivity($action = 'delete', $description = 'delete data', $data = [])
{
    $ci = &get_instance();

    $ci->db->insert('logactivity', array(
        'action' => $action,
        'description' => $description,
        'data' => json_encode($data),
        'createdby' => getCurrentIdUser(),
        'createddate' => getCurrentDate()
    ));
}

function downloadFile($url)
{
    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
    ));

    $response = curl_exec($curl);
    curl_close($curl);

    $filename = 'application/cache/' . time() . '.png';
    file_put_contents($filename, $response);

    $base64 = base64_encode(file_get_contents($filename));
    unlink($filename);

    return 'data:image/png;base64,' . $base64;
}

function getTotalVillagesByCity($cityname)
{
    $CI = &get_instance();

    return $CI->db->select('COUNT(*) AS total')
        ->from('msusers')
        ->where('kabkotaname', $cityname)
        ->where('role', 'Villages')
        ->get()
        ->row()->total;
}

function getTotalVillagesByDistrict($districtname)
{
    $CI = &get_instance();

    return $CI->db->select('COUNT(*) AS total')
        ->from('msusers')
        ->where('kecamatanname', $districtname)
        ->where('role', 'Villages')
        ->get()
        ->row()->total;
}

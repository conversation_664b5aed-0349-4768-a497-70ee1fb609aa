<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!-- [ breadcrumb ] start -->
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col-md-12">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url(); ?>">Home</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('master/' . $feature); ?>"><?= $element['title'] ?></a></li>
                    <li class="breadcrumb-item" aria-current="page">Detail</li>
                </ul>
            </div>

            <div class="col-md-12">
                <div class="page-header-title">
                    <h2 class="mb-0"><?= $element['title'] ?></h2>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- [ breadcrumb ] end -->

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <?php foreach ($element['fields'] as $key => $value) : ?>
                    <?php if ($value['type'] == 'row') : ?>
                        <div class="row">
                            <?php foreach ($value['data'] as $key_data => $value_data) : ?>
                                <div class="<?= $value_data['parent_class'] ?>">
                                    <div class="mb-3">
                                        <label class="form-label"><?= $value_data['label'] ?></label>
                                        <div class="form-control-plaintext">
                                            <?= $value_data['value'] ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>

                <div class="mt-3">
                    <?php foreach ($element['footer_button'] as $key => $value) : ?>
                        <?php if ($value['type'] == 'a') : ?>
                            <a href="<?= $value['href'] ?>" class="<?= $value['class'] ?>"><?= $value['text'] ?></a>
                        <?php elseif ($value['type'] == 'button') : ?>
                            <button type="<?= $value['type'] ?>" class="<?= $value['class'] ?>"><?= $value['text'] ?></button>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

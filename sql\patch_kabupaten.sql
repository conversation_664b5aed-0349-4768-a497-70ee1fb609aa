-- SQL Patch: Create table kabupaten

CREATE TABLE IF NOT EXISTS `kabupaten` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nama_kabupaten` varchar(150) DEFAULT NULL,
  `kode_kabupaten` varchar(10) DEFAULT NULL,
  `provinsi` varchar(100) DEFAULT NULL,
  `alamat` varchar(255) DEFAULT NULL,
  `keterangan` text DEFAULT NULL,
  `createddate` datetime DEFAULT NULL,
  `createdby` int(11) DEFAULT NULL,
  `updateddate` datetime DEFAULT NULL,
  `updatedby` int(11) DEFAULT NULL,
  `userid` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE `kabupaten`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `kabupaten`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;
